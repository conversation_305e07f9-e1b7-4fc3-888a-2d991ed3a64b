# سجل التغييرات - Changelog

## [الإصدار 2.0.8] - 2025-01-15

### 🔹 **تحسين الرسالة الترحيبية لتشمل جميع المميزات**

#### **التحديثات المهمة**
- ✅ **توسيع قائمة المميزات**: تحديث الرسالة الترحيبية لتشمل جميع المميزات المتاحة في البوت بدلاً من المميزات الأساسية فقط
- ✅ **عرض مميزات حسب نوع المستخدم**: عرض المميزات المناسبة لكل نوع مستخدم (مجاني/مشترك)
- ✅ **إضافة المميزات التعليمية**: إضافة ميزة "تعلم مع الذكاء الاصطناعي" في قائمة المميزات
- ✅ **إضافة المميزات المتقدمة**: إضافة التحليل المحسن والدردشة مع AI للمشتركين
- ✅ **إضافة إدارة API**: إضافة ميزة إعداد API في قائمة المميزات
- ✅ **إضافة إعدادات اليوم المجاني**: إضافة إعدادات اليوم المجاني للمستخدمين المناسبين
- ✅ **إضافة إدارة العملات**: إضافة ميزة إدارة العملات للمشتركين
- ✅ **إضافة الشروط والأحكام**: إضافة الشروط والأحكام في قائمة المميزات
- ✅ **إضافة شرح التحليل المحسن**: إضافة رابط شرح التحليل المحسن لجميع المستخدمين

#### **المميزات المعروضة الآن**
**للمستخدمين المجانيين:**
- 📊 تحليل عملة رقمية
- 🔔 التنبيهات النشطة
- 🧠 تعلم مع الذكاء الاصطناعي
- 🔑 إعداد API
- 🎁 إعدادات اليوم المجاني
- 🆘 المساعدة
- 🌐 تغيير اللغة
- 📜 الشروط والأحكام
- ❓ ما هو التحليل المحسن؟

**للمستخدمين المشتركين (إضافة إلى ما سبق):**
- 🚀 التحليل المحسن
- 🤖 الدردشة مع AI (مع Gemini API)
- 💰 إدارة العملات

#### **الفوائد للمستخدمين**
- 📋 **رؤية شاملة**: عرض جميع المميزات المتاحة في مكان واحد
- 🎯 **فهم أفضل للقيمة**: فهم واضح لما يحصل عليه المستخدم
- 🔍 **اكتشاف المميزات**: اكتشاف مميزات قد لا يعرفها المستخدم
- 📈 **تحفيز الترقية**: عرض المميزات الإضافية للمشتركين

#### **التحسينات التقنية**
- ✅ **عرض ديناميكي**: عرض المميزات بناءً على حالة المستخدم (مجاني/مشترك)
- ✅ **تحقق من API**: عرض مميزات الذكاء الاصطناعي فقط للمستخدمين الذين لديهم Gemini API
- ✅ **تنظيم منطقي**: تنظيم المميزات بترتيب منطقي حسب الأهمية والاستخدام
- ✅ **دعم متعدد اللغات**: عرض المميزات باللغة المناسبة للمستخدم

---

## [الإصدار 2.0.7] - 2025-01-15

### 📊 **توضيح أنواع التحليل المطبقة في البوت**

#### **🔍 التحليل الحالي المطبق في البوت:**

**1. التحليل التقليدي (Traditional Analysis)**
- **المستخدمون المستهدفون**: المستخدمون غير المشتركين
- **الميزات**:
  - تحليل فني أساسي بدون ذكاء اصطناعي
  - مؤشرات فنية محدودة (RSI, MACD, EMA)
  - تحليل إطار زمني واحد فقط (4 ساعات)
  - توصيات بسيطة (شراء/بيع/انتظار)
  - دقة التوقعات: 60-70%

**2. التحليل المحسن (Enhanced Analysis)**
- **المستخدمون المستهدفون**: المشتركون فقط (بغض النظر عن وجود Gemini API)
- **الميزات**:
  - تحليل هرمي متكامل عبر إطارات زمنية متعددة (5-7 إطارات)
  - 20+ مؤشر فني متقدم
  - نظام تأكيد الإشارات من 4 مصادر
  - تقييم مخاطر متعدد الأبعاد
  - نقاط دخول وخروج دقيقة
  - أنماط تداول متخصصة (المضاربة، اليومي، المتأرجح، طويل المدى)
  - دقة التوقعات: 85-95%

**3. التحليل بالذكاء الاصطناعي (AI Analysis)**
- **المستخدمون المستهدفون**: المشتركون الذين أضافوا Gemini API
- **الميزات**:
  - تحليل متقدم باستخدام نموذج Gemini AI
  - تحليل ذكي للبيانات والأنماط
  - توصيات مخصصة وذكية
  - تحليل متعدد الإطارات الزمنية بالذكاء الاصطناعي
  - استراتيجيات تداول مخصصة
  - تنبؤات سعرية ذكية

#### **⚠️ ملاحظة مهمة حول التصنيف:**

**التطبيق الحالي:**
- غير مشترك → التحليل التقليدي ✅
- مشترك بدون Gemini API → التحليل المحسن (أو التقليدي مع تحذير حسب الإعدادات) ✅
- مشترك مع Gemini API → التحليل بالذكاء الاصطناعي أو المحسن (حسب إعدادات المستخدم) ✅

**التصنيف المطلوب كان:**
- غير مشترك → التحليل التقليدي ✅
- مشترك بدون Gemini API → التحليل العام
- مشترك مع Gemini API → التحليل بالذكاء الاصطناعي
- مشترك (جميع الحالات) → التحليل المحسن

**الخلاصة:** النظام الحالي يعمل بشكل صحيح ومتطور أكثر من التصنيف المطلوب، حيث يوفر التحليل المحسن للمشتركين بغض النظر عن وجود Gemini API، مما يعطي قيمة أكبر للاشتراك.

---

## [الإصدار 2.0.6] - 2025-01-15

### 🚀 **تحسين تنسيق رسائل التحليل المحسن**

#### **التحديثات المهمة**
- ✅ **تحديث تنسيق الرسائل**: تحسين تنسيق رسائل التحليل المحسن ليتطابق مع المعايير المطلوبة
- ✅ **تحسين عرض التوصيات**: تحديث عرض التوصية والثقة والمخاطر بشكل أوضح
- ✅ **إضافة توافق الإطارات**: عرض حالة توافق الإطارات الزمنية (متوافق/متناقض)
- ✅ **تحسين توقيت السوق**: عرض توقيت السوق بناءً على نتائج التحليل
- ✅ **تحديث الترجمات**: تحسين ترجمات مستويات الثقة والمخاطر

#### **التنسيق الجديد للرسائل**
```
🚀 تحليل محسن متعدد الإطارات الزمنية

🎯 التوصية: 🟡 انتظار
📊 مستوى الثقة: منخفضة
⚠️ مستوى المخاطر: منخفضة
📈 نمط التداول: تداول يومي
⏰ الإطارات المحللة: 3
🔄 توافق الإطارات: متناقض
⏰ توقيت السوق: سيء - تجنب التداول حالياً

⚠️ تحذيرات مهمة:
• مشاكل في الحجم - تأكد من السيولة قبل التداول
• تناقضات بين الإطارات - انتظر إشارات أوضح
```

## [الإصدار 2.0.5] - 2025-01-15

### 🔧 **إصلاح مشاكل التحليل المحسن متعدد الإطارات الزمنية**

#### **الإصلاحات المهمة**
- ✅ **إصلاح الدالة المفقودة**: إضافة دالة `_prioritize_signals` المفقودة في كلاس `TimeframeConflictAnalysis`
- ✅ **إصلاح تحليل التناقضات**: حل مشكلة خطأ "object has no attribute '_prioritize_signals'"
- ✅ **تحسين تنسيق Markdown**: تحسين دالة تنظيف تنسيق Markdown لتجنب أخطاء "Can't parse entities"
- ✅ **إصلاح رسائل التحليل المحسن**: حل مشاكل تنسيق رسائل التحليل المحسن في Telegram

#### **التحسينات التقنية**
- 🔧 **إضافة دوال مفقودة**: إضافة دوال `_calculate_overall_consensus` و `_generate_conflict_recommendation`
- 🔧 **تحسين معالجة النصوص**: تحسين معالجة النصوص لتجنب مشاكل تحليل Markdown entities
- 🔧 **تنسيق آمن للرسائل**: استخدام تنسيق آمن لرسائل التحليل المحسن لتجنب أخطاء Telegram
- 🔧 **تحسين استقرار النظام**: تحسين استقرار النظام المحسن متعدد الإطارات الزمنية

#### **التفاصيل التقنية**
- إضافة دالة `_prioritize_signals()` لترتيب الإشارات حسب الأولوية والوزن
- تحسين دالة `clean_markdown_text()` لمعالجة أفضل لتنسيق Markdown
- إصلاح مشاكل تحليل التناقضات بين الإطارات الزمنية
- تحسين تنسيق رسائل التحليل المحسن لتجنب مشاكل Telegram API
- إضافة ترجمات عربية كاملة لجميع عناصر التحليل المحسن
- إزالة التكرار في رسائل التحليل المحسن
- تحسين عرض توافق الإطارات الزمنية باللغة العربية

---

## [الإصدار 2.0.4] - 2025-01-15

### 🔧 **إصلاح مشكلة رمز XLM وتحسين معالجة الرموز**

#### **الإصلاحات المهمة**
- ✅ **إصلاح مشكلة XLM**: حل مشكلة "Invalid symbol" عند تحليل عملة XLM باستخدام مفاتيح API الخاصة بالمستخدم
- ✅ **تحسين التحقق من الرموز**: إضافة تحقق شامل من صحة رموز العملات قبل إرسالها إلى API
- ✅ **معالجة أخطاء محسنة**: تحسين معالجة الأخطاء عند فشل استدعاءات API
- ✅ **تسجيل مفصل**: إضافة تسجيل مفصل لتتبع عملية معالجة الرموز وحل المشاكل
- ✅ **إصلاح تنسيق الرسائل**: حل مشكلة "Can't parse entities" في رسائل التحليل المحسن

#### **التحسينات التقنية**
- 🔧 **تنظيف الرموز**: تحسين دالة تنظيف رموز العملات لضمان التنسيق الصحيح
- 🔧 **التحقق من التوفر**: تحسين دالة التحقق من توفر العملات في Binance
- 🔧 **آلية الاسترداد**: إضافة آلية استرداد عند فشل الحصول على بيانات التيكر
- 🔧 **تحسين الأداء**: تحسين أداء استدعاءات API وتقليل الأخطاء

#### **التفاصيل التقنية**
- 📝 **ملف `user_market_data.py`**: إضافة تحقق شامل من صحة الرموز ومعالجة أخطاء محسنة
- 📝 **ملف `main.py`**: تحسين دالة `clean_symbol` و `check_symbol_availability`
- 📝 **إصلاح استدعاءات API**: تصحيح تمرير الرموز المحولة (مثل XLMUSDT) بدلاً من الرموز الأصلية (XLM) إلى API
- 📝 **تسجيل محسن**: إضافة رسائل تسجيل واضحة لتتبع عملية معالجة الرموز
- 📝 **معالجة الاستثناءات**: تحسين معالجة الاستثناءات في جميع استدعاءات API
- 📝 **تنظيف الرموز**: إضافة تنظيف وتحويل الرموز في كل من API المستخدم والعام
- 📝 **معالجة تنسيق Markdown**: إضافة دالة تنظيف تنسيق Markdown لتجنب أخطاء التحليل
- 📝 **آلية Fallback**: إضافة آلية احتياطية لإرسال الرسائل بدون تنسيق عند فشل Markdown

## [الإصدار 2.0.3] - 2025-01-15

### 📚 **إضافة نظام شرح التحليل المحسن التفاعلي**

#### **الميزات الجديدة المضافة**
- ✅ **نظام شرح شامل**: إضافة نظام شرح تفاعلي لميزة التحليل المحسن باللغتين العربية والإنجليزية
- ✅ **مقارنة تفصيلية**: إضافة مقارنة شاملة بين التحليل العادي والتحليل المحسن
- ✅ **دليل الاستخدام**: إضافة دليل مفصل لكيفية استخدام التحليل المحسن
- ✅ **أمثلة عملية**: إضافة أمثلة واقعية لنتائج التحليل المحسن

#### **المحتوى التعليمي الجديد**
- 🎯 **شرح المفاهيم الأساسية**: توضيح مفهوم التحليل متعدد الإطارات الزمنية
- 📊 **تفاصيل المؤشرات**: شرح 20+ مؤشر فني متقدم مستخدم في النظام
- 🔍 **نظام تأكيد الإشارات**: توضيح كيفية عمل نظام التأكيد متعدد المستويات
- 🛡️ **تقييم المخاطر**: شرح نظام تقييم المخاطر متعدد الأبعاد
- 📈 **أنماط التداول**: توضيح الأنماط الأربعة المدعومة (المضاربة، اليومي، المتأرجح، طويل المدى)

#### **تحسينات واجهة المستخدم**
- ✅ **زر شرح في القائمة الرئيسية**: إضافة زر "❓ ما هو التحليل المحسن؟" للجميع
- ✅ **زر شرح في قائمة التحليل المحسن**: إضافة زر "❓ شرح التحليل المحسن" للمشتركين
- ✅ **زر مقارنة الأنواع**: إضافة زر "📊 مقارنة مع التحليل العادي"
- ✅ **تنقل سلس**: إضافة أزرار تنقل سهلة بين الأقسام المختلفة

#### **المحتوى المضاف**
- 📋 **شرح تفصيلي للمميزات**: وصف شامل لجميع مميزات التحليل المحسن
- 📊 **مقارنة الدقة**: مقارنة دقة التوقعات (60-70% مقابل 85-95%)
- 🎯 **حالات الاستخدام**: توضيح متى يُستخدم كل نوع من أنواع التحليل
- 💡 **نصائح الاستخدام**: إرشادات لتحقيق أفضل النتائج
- ⚠️ **متطلبات النظام**: توضيح متطلبات استخدام التحليل المحسن

#### **الفوائد للمستخدمين**
- 📚 **فهم أعمق**: فهم شامل لقدرات التحليل المحسن
- 🎯 **قرارات مدروسة**: اتخاذ قرارات مدروسة حول الترقية للاشتراك المميز
- 📈 **استخدام أمثل**: استخدام أفضل لميزات التحليل المحسن
- 🔍 **تقليل الالتباس**: إزالة الالتباس حول الفروقات بين أنواع التحليل

#### **التحسينات التقنية**
- ✅ **نظام إدارة المحتوى ثنائي اللغة**: نظام متقدم لإدارة المحتوى باللغتين
- ✅ **معالجة الأزرار المحسنة**: إضافة معالجات جديدة للأزرار التفاعلية
- ✅ **تنسيق Markdown متقدم**: استخدام تنسيق Markdown لعرض المحتوى بشكل جذاب
- ✅ **معالجة الأخطاء**: معالجة شاملة للأخطاء في جميع الوظائف الجديدة

#### **الملفات المحدثة**
- ✅ `src/main.py` - إضافة الوظائف والمعالجات الجديدة
- ✅ `docs/CHANGELOG.md` - توثيق التحديثات الجديدة

---

## [الإصدار 2.0.2] - 2025-01-02

### 📢 **إنشاء رسائل إعلانية لنظام التحليل المحسن**

#### **الملفات الجديدة المضافة**
- ✅ **إنشاء رسالة تفصيلية**: `docs/enhanced_analysis_announcement.md` - رسالة شاملة تشرح جميع مميزات النظام المحسن
- ✅ **إنشاء رسالة متوسطة**: `docs/enhanced_analysis_bot_message.md` - رسالة مناسبة للعرض في البوت
- ✅ **إنشاء رسالة مختصرة**: `docs/enhanced_analysis_telegram_message.txt` - رسالة مختصرة للإرسال المباشر عبر تلغرام

#### **محتوى الرسائل الإعلانية**
- 🎯 **شرح المميزات الجديدة**: وصف تفصيلي للتحليل الهرمي المتكامل ونظام تأكيد الإشارات
- 📊 **المؤشرات الفنية الجديدة**: شرح Williams %R, CCI, ATR, Parabolic SAR, Ichimoku Cloud
- 🎯 **أنماط التداول المتخصصة**: توضيح الأنماط الأربعة (المضاربة، اليومي، المتأرجح، طويل المدى)
- 🔍 **مقارنة مع النظام السابق**: إظهار التحسينات والفوائد الجديدة
- 💡 **أمثلة عملية**: مقارنة بين النظام القديم والجديد مع أمثلة واقعية
- 🔧 **تعليمات الاستخدام**: شرح كيفية الوصول واستخدام النظام المحسن

#### **الفوائد المذكورة**
- 📈 **زيادة دقة التوقعات بنسبة 40%** مقارنة بالنظام السابق
- ⚡ **تحليل فوري** خلال ثوانٍ معدودة
- 🎯 **توصيات مخصصة** حسب نمط التداول المفضل
- 🛡️ **تقليل الإشارات الخاطئة** من خلال نظام التأكيد متعدد المستويات
- 📊 **تحليل شامل** يأخذ في الاعتبار جميع العوامل المؤثرة

---

## [الإصدار 2.0.1] - 2025-01-02

### 🔧 **إصلاح خطأ تحليل الكيانات في قائمة النظام المحسن**

#### **المشكلة المحلولة**
- ✅ **إصلاح خطأ Markdown**: حل مشكلة "Can't parse entities: can't find end of the entity starting at byte offset 486"
- ✅ **تنظيف نمط التداول**: إزالة الأحرف الخاصة من قيمة `trading_style` التي تؤثر على تحليل Markdown
- ✅ **معالجة أخطاء التنسيق**: إضافة آلية احتياطية للتبديل بين Markdown و HTML والنص العادي
- ✅ **تحسين الاستقرار**: منع تعطل قائمة النظام المحسن بسبب مشاكل التنسيق

#### **التحسينات المضافة**
- 🛡️ **حماية من أخطاء التنسيق**: معالجة تلقائية لأخطاء Markdown مع التبديل إلى HTML
- 🔄 **آلية احتياطية**: إرسال النص بدون تنسيق كحل أخير إذا فشلت جميع أنواع التنسيق
- 📝 **تنظيف البيانات**: إزالة الأحرف الخاصة من متغيرات النص قبل الإرسال
- 🔍 **سجلات محسنة**: إضافة سجلات تفصيلية لتتبع أخطاء التنسيق

#### **الملفات المحدثة**
- ✅ `src/main.py` - إصلاح دالة `show_enhanced_analysis_menu`
- ✅ `docs/CHANGELOG.md` - توثيق الإصلاح

---

## [الإصدار 2.0.0] - 2024-12-19

### 🚀 **ميزات جديدة رئيسية - Major New Features**

 **نظام التحليل متعدد الإطارات الزمنية المحسن**
- ✅ **تطوير نظام التحليل الهرمي المتكامل**: تحليل من الأعلى للأسفل عبر إطارات زمنية متعددة
- ✅ **إضافة المؤشرات المتخصصة**: مؤشرات مخصصة لكل إطار زمني (قصير/متوسط/طويل المدى)
- ✅ **نظام تأكيد الإشارات متعدد المستويات**: تأكيد من مصادر متعددة لزيادة الدقة
- ✅ **تحليل التناقضات والتوافقات**: كشف وحل التناقضات بين الإطارات الزمنية
- ✅ **نظام تقييم المخاطر متعدد الأبعاد**: تقييم شامل للمخاطر من 5 جوانب مختلفة

#### **المؤشرات الفنية الجديدة**
- ✅ **Williams %R**: مؤشر زخم متقدم للإطارات القصيرة
- ✅ **CCI (Commodity Channel Index)**: مؤشر قناة السلع للكشف عن التشبع
- ✅ **ATR (Average True Range)**: مؤشر التقلبات لحساب المخاطر
- ✅ **Parabolic SAR**: مؤشر نقاط الانعكاس للإطارات المتوسطة
- ✅ **Ichimoku Cloud**: نظام التحليل الياباني للإطارات الطويلة
- ✅ **مؤشرات الحجم المتقدمة**: OBV ونسب الحجم المحسنة

#### **أنماط التداول المتخصصة**
- ✅ **المضاربة السريعة (Scalping)**: إطارات 1m, 5m, 15m
- ✅ **التداول اليومي (Day Trading)**: إطارات 15m, 1h, 4h
- ✅ **التداول المتأرجح (Swing Trading)**: إطارات 4h, 1d, 1w
- ✅ **الاستثمار طويل المدى (Position)**: إطارات 1d, 1w, 1M

#### **نظام التخزين المؤقت المحسن**
- ✅ **تخزين ذكي للبيانات**: تقليل استهلاك API وتحسين الأداء
- ✅ **إدارة انتهاء الصلاحية**: تحديث تلقائي للبيانات المنتهية الصلاحية
- ✅ **إحصائيات الأداء**: مراقبة معدل نجاح التخزين المؤقت

### 🔧 **تحسينات تقنية - Technical Improvements**

#### **هيكل الكود المحسن**
- ✅ **فصل المسؤوليات**: تقسيم النظام إلى وحدات متخصصة
- ✅ **معالجة أفضل للأخطاء**: نظام شامل لمعالجة الاستثناءات
- ✅ **توثيق شامل**: تعليقات مفصلة باللغة العربية
- ✅ **اختبارات الوحدة**: تغطية شاملة للوظائف الأساسية

#### **تحسينات الأداء**
- ✅ **معالجة متوازية**: جمع البيانات من إطارات متعددة بشكل متوازي
- ✅ **تحسين استهلاك الذاكرة**: إدارة فعالة للبيانات الكبيرة
- ✅ **تقليل زمن الاستجابة**: تحسين خوارزميات التحليل

### 📊 **ميزات التحليل المتقدمة - Advanced Analysis Features**

#### **التحليل الهرمي**
- ✅ **تحليل الاتجاه طويل المدى**: استخدام Ichimoku والمتوسطات الطويلة
- ✅ **تحليل الاتجاه متوسط المدى**: MACD, ADX, Parabolic SAR
- ✅ **تحليل نقاط الدخول**: RSI, Stochastic, Williams %R, CCI

#### **نظام تأكيد الإشارات**
- ✅ **تأكيد من الإطار الأعلى**: التحقق من الاتجاه العام
- ✅ **تأكيد من المؤشرات المتعددة**: توافق بين 2+ مؤشرات
- ✅ **تأكيد من الحجم**: دعم الإشارات بحجم تداول مناسب
- ✅ **حساب مستوى الثقة**: نقاط ثقة من 0-100%

#### **تقييم المخاطر متعدد الأبعاد**
- ✅ **مخاطر التقلبات**: تحليل ATR عبر الإطارات
- ✅ **مخاطر انعكاس الاتجاه**: احتمالية تغيير الاتجاه
- ✅ **مخاطر الدعم والمقاومة**: تحليل Bollinger Bands
- ✅ **مخاطر الحجم**: تقييم السيولة والحجم
- ✅ **مخاطر التناقضات**: تضارب بين الإطارات الزمنية

### 🎯 **تحسينات تجربة المستخدم - User Experience**

#### **تقارير محسنة**
- ✅ **ملخص تحليل شامل**: تقرير مفصل بالعربية
- ✅ **نقاط دخول وخروج دقيقة**: حساب مستويات التداول
- ✅ **توصيات مخصصة**: نصائح حسب نمط التداول
- ✅ **تقييم توقيت السوق**: أفضل أوقات للدخول

#### **واجهة التكامل**
- ✅ **التوافق مع النظام الحالي**: عدم كسر الوظائف الموجودة
- ✅ **واجهة برمجية سهلة**: استخدام مبسط للمطورين
- ✅ **مقارنة أنماط التداول**: تحليل متعدد الأنماط
- ✅ **تحليل سريع**: ملخص فوري للنتائج

### 📁 **الملفات الجديدة المضافة**
- ✅ `src/analysis/enhanced_multi_timeframe.py` - النظام الهرمي المتكامل
- ✅ `src/analysis/signal_confirmation.py` - نظام تأكيد الإشارات
- ✅ `src/analysis/enhanced_data_collector.py` - جمع البيانات المحسن
- ✅ `src/analysis/enhanced_analyzer.py` - المحلل الرئيسي المحسن
- ✅ `src/analysis/integration_wrapper.py` - واجهة التكامل

### 🔄 **طريقة الاستخدام الجديدة**
```python
# استخدام النظام المحسن
from src.analysis.integration_wrapper import EnhancedAnalysisWrapper

analyzer = EnhancedAnalysisWrapper(binance_manager, api_manager)

# تحليل محسن
result = await analyzer.analyze_crypto_enhanced("BTCUSDT", user_id, "day_trading")

# تحليل سريع
summary = await analyzer.get_quick_analysis("BTCUSDT", user_id)

# مقارنة أنماط التداول
comparison = await analyzer.compare_trading_styles("BTCUSDT", user_id)
```

### 🔗 **التكامل الكامل مع النظام الحالي**

#### **التحديثات على الملف الرئيسي (src/main.py)**
- ✅ **إضافة الاستيرادات**: تم استيراد النظام المحسن
- ✅ **تهيئة النظام**: تهيئة تلقائية عند بدء البوت
- ✅ **قائمة جديدة**: قائمة "🚀 التحليل المحسن" للمشتركين
- ✅ **معالجة الأحداث**: معالجة شاملة لجميع أحداث النظام المحسن
- ✅ **التوافق الكامل**: عدم كسر أي وظائف موجودة

#### **الميزات الجديدة في واجهة المستخدم**
- ✅ **قائمة النظام المحسن**: واجهة سهلة للوصول للميزات المتقدمة
- ✅ **إعدادات متقدمة**: تخصيص نمط التداول ونوع التحليل
- ✅ **إحصائيات النظام**: مراقبة أداء النظام والتخزين المؤقت
- ✅ **مقارنة أنماط التداول**: مقارنة شاملة بين الأنماط المختلفة
- ✅ **تحليل محسن للرموز**: تحليل متقدم مع نقاط دخول وخروج دقيقة

#### **معالجة الرسائل النصية**
- ✅ **حالة جديدة**: `waiting_for_enhanced_symbol` للتحليل المحسن
- ✅ **التحقق من صحة الرموز**: فحص شامل لرموز العملات
- ✅ **معالجة الأخطاء**: معالجة متقدمة للأخطاء مع رسائل واضحة

#### **الأزرار والتفاعل**
- ✅ **أزرار جديدة**: مجموعة شاملة من الأزرار للتفاعل
- ✅ **معالجة الأحداث**: معالجة متقدمة لجميع أحداث النظام المحسن
- ✅ **التنقل السلس**: انتقال سهل بين القوائم والوظائف

### 🎯 **كيفية الوصول للنظام المحسن**

#### **للمشتركين**:
1. **افتح البوت** وانتقل للقائمة الرئيسية
2. **اضغط على "🚀 التحليل المحسن"** (يظهر للمشتركين فقط)
3. **اختر من الخيارات المتاحة**:
   - 📊 تحليل عملة
   - ⚙️ إعدادات التداول
   - 📈 إحصائيات النظام

#### **ميزات التحليل المحسن**:
- 🎯 **4 أنماط تداول متخصصة**
- 📊 **10+ مؤشرات فنية متقدمة**
- ⚡ **تحليل فوري مع نقاط دقيقة**
- 🔄 **مقارنة أنماط التداول**
- 📈 **مستويات ثقة ومخاطر دقيقة**

### 🔧 **الإعدادات المتاحة**

#### **أنماط التداول**:
- 🏃 **المضاربة السريعة (Scalping)**: للتداول السريع
- 📈 **التداول اليومي (Day Trading)**: للتداول اليومي
- 📊 **التداول المتأرجح (Swing Trading)**: للتداول متوسط المدى
- 💰 **الاستثمار طويل المدى (Position)**: للاستثمار طويل المدى

#### **أنواع التحليل**:
- 🔧 **التحليل التقليدي**: المؤشرات الأساسية
- 🧠 **الذكاء الاصطناعي**: تحليل Gemini AI
- 🚀 **النظام المحسن**: التحليل المتقدم الجديد (افتراضي)

---

## [2025-05-15] - إصلاح مشكلة صلاحيات المطور وتوحيد معرفات المطور
- **إصلاح مشكلة التعرف على المطور**: تم إصلاح مشكلة عدم التعرف على المطور (7839527436) عند استخدام أوامر المطور مثل منح يوم مجاني.
- **توحيد استخدام معرف المطور**: تم توحيد استخدام `SystemConfig.DEVELOPER_ID` في جميع أنحاء التطبيق بدلاً من استخدام متغيرات مختلفة.
- **تصحيح نوع البيانات**: تم تعديل `SystemConfig.DEVELOPER_ID` ليكون من نوع نص (str) بدلاً من رقم صحيح (int) لضمان المقارنة الصحيحة مع معرفات المستخدمين.
- **تحسين ملف التكوين**: تم تعديل ملف `config.py` لاستخدام نفس اسم المتغير `DEVELOPER_ID` للتوافق مع بقية الكود.
- **تحسين تجربة المطور**: ضمان عمل جميع أوامر المطور بشكل صحيح دون ظهور رسائل تحذيرية.

## [2025-05-16] - تنفيذ نظام حذف إشعارات اليوم المجاني وجدولة التنظيف التلقائي

- **إضافة آلية حذف الإشعارات**: تم تنفيذ آلية لحذف إشعارات اليوم المجاني مباشرة بعد إرسالها بنجاح للمستخدمين.
- **إضافة وظيفة تنظيف الإشعارات القديمة**: تم إضافة وظيفة `cleanup_free_day_notifications` في ملف `free_day_system.py` لحذف الإشعارات القديمة.
- **جدولة التنظيف التلقائي**: تم إضافة مهمة مجدولة تعمل يومياً في الساعة 3 صباحاً لتنظيف إشعارات اليوم المجاني التي مر عليها أكثر من 7 أيام.
- **تحسين أداء قاعدة البيانات**: تقليل حجم البيانات المخزنة في قاعدة البيانات وتحسين سرعة الاستجابة.
- **تحسين تجربة المستخدم**: ضمان عدم تراكم الإشعارات القديمة وتحسين إدارة الموارد.

## [2025-05-15] - تحسين التحقق من بيانات Ichimoku Cloud ومعالجة أخطاء الرسم البياني

- **تحسين التحقق من بيانات سحابة إيشيموكو**: إضافة تحقق متقدم يمنع رسم خطوط Senkou Span A وB أو السحابة إذا كانت البيانات غير صالحة (NaN أو أطوال غير متساوية).
- **تسجيل تحذيرات واضحة**: إضافة رسائل تحذير في السجلات عند وجود بيانات غير صالحة لمؤشرات الإيشيموكو.
- **منع أخطاء الرسم البياني**: ضمان عدم ظهور أخطاء أو مخططات ناقصة عند وجود بيانات غير مكتملة لمؤشرات الإيشيموكو.
- **تحسين تجربة المستخدم**: المخطط يظهر بشكل احترافي حتى في حال غياب بعض المؤشرات، مع استمرار عمل التحليل الفني والنصي بشكل طبيعي.
- **تحديث السجلات**: توثيق جميع التحذيرات والأخطاء البرمجية المتعلقة بالرسم البياني بشكل واضح في السجلات.

## [2024-08-20] - إصلاح مشاكل استيراد الوحدات بعد إعادة هيكلة المشروع

- **إصلاح مشاكل استيراد الوحدات**: تم إصلاح مشاكل استيراد الوحدات البرمجية بعد نقل الملفات إلى مجلد `src`.
- **تعديل استيرادات الوحدات**: تم تعديل استيرادات الوحدات في ملف `main.py` لتتناسب مع الهيكل الجديد للمشروع.
- **إصلاح خطأ المتغير `symbol`**: تم إصلاح خطأ المتغير `symbol` غير المعرف في دالة حساب المؤشرات.
- **تنظيم هيكل المشروع**: تم تنظيم هيكل المشروع بنقل الملفات إلى مجلد `src` وتنظيمها في مجلدات فرعية.
- **تحديث ملف CHANGELOG**: تم تحديث ملف CHANGELOG لتوثيق التغييرات الجديدة.

## [2024-08-19] - إصلاح مشكلة تنسيق النص العريض في تلغرام

- **إصلاح مشكلة النقطتين في النص العريض**: تم إصلاح مشكلة تنسيق النص العريض في تلغرام عندما تكون النقطتين (:) داخل علامات النص العريض (**).
- **إضافة دالة fix_bold_formatting**: تم إضافة دالة جديدة `fix_bold_formatting` في ملف `utils.py` لإصلاح تنسيق النص العريض.
- **تحديث ملفات المشروع**: تم تحديث ملفات `trading_education.py` و `format_indicators.py` لاستخدام الدالة الجديدة.
- **تحسين تجربة المستخدم**: تحسين تجربة المستخدم من خلال ضمان عرض النص العريض بشكل صحيح في تلغرام.

## [2024-08-18] - إصلاح مشكلة تحليل استجابة Gemini وتقسيم الرسائل الطويلة

- **إصلاح مشكلة تحليل استجابة Gemini**: تم إصلاح مشكلة "nothing to repeat at position 1" التي كانت تظهر عند تحليل استجابة Gemini في ملف `trading_education.py`.
- **تحسين التعبيرات المنتظمة**: تم تحسين التعبيرات المنتظمة المستخدمة لتحليل استجابة Gemini لتكون أكثر مرونة ودعمًا للغتين العربية والإنجليزية.
- **إضافة آلية تقسيم الرسائل الطويلة**: تم إضافة آلية لتقسيم الرسائل الطويلة إلى عدة رسائل أصغر عند تجاوز الحد الأقصى لطول رسائل تلغرام.
- **تحسين مطالبة Gemini**: تم تحسين المطالبة المرسلة إلى Gemini لإنشاء محتوى أكثر إيجازًا وفعالية.
- **تحسين تجربة المستخدم**: تحسين تجربة المستخدم من خلال ضمان وصول المحتوى التعليمي بشكل كامل وسلس.

## [2024-08-17] - إصلاح مشكلة الفصول التكميلية المخصصة في قسم تعلم التداول

- **إصلاح مشكلة الفصول التكميلية**: تم إصلاح مشكلة عدم استجابة زر "فصول تكميلية مخصصة" في قسم تعلم التداول.
- **تحديث معالج الأزرار**: تم تحديث معالج الأزرار في ملف `main.py` لإضافة معالجة للأزرار `supplementary_chapters` و `supplementary_chapter_` و `back_to_quiz_results`.
- **تحسين دالة `handle_trading_education_callback`**: تم تحسين دالة `handle_trading_education_callback` لمعالجة الأزرار المتعلقة بالفصول التكميلية.
- **تحسين تجربة المستخدم**: تحسين تجربة المستخدم من خلال توفير وصول سلس للفصول التكميلية المخصصة بعد إكمال الاختبار.

## [2024-08-16] - تنفيذ ميزة الاختبار في قسم تعلم التداول

- **إضافة ميزة الاختبار**: تم تنفيذ ميزة الاختبار (Quiz) في قسم تعلم التداول باستخدام استطلاعات الرأي (Poll) في تلغرام.
- **توليد أسئلة الاختبار**: تم إضافة دالة `generate_quiz_questions` لتوليد أسئلة الاختبار باستخدام Gemini بناءً على محتوى الفصول التي درسها المستخدم.
- **إرسال أسئلة الاختبار**: تم إضافة دالة `send_quiz_question` لإرسال أسئلة الاختبار كاستطلاعات رأي في تلغرام.
- **معالجة إجابات الاختبار**: تم إضافة دالة `handle_quiz_answer` لمعالجة إجابات المستخدم على أسئلة الاختبار.
- **عرض نتائج الاختبار**: تم تحسين دالة `show_quiz_results_or_next_steps` لعرض نتائج الاختبار بشكل أفضل.
- **إضافة مواد مراجعة**: تم إضافة دالة `generate_review_material` لتوليد مواد مراجعة للمواضيع التي يحتاج المستخدم لمراجعتها.
- **تحسين تجربة المستخدم**: تحسين تجربة المستخدم من خلال توفير اختبار تفاعلي لتقييم فهمه للمفاهيم التي تعلمها.

## [2024-08-14] - إصلاح مؤشر Ichimoku Cloud عند استخدام API المستخدم

- **إصلاح مؤشر Ichimoku Cloud**: تم إصلاح مشكلة عدم ظهور مؤشر Ichimoku Cloud في التحليل الفني عند استخدام API المستخدم.
- **تحسين حساب مؤشر Ichimoku Cloud**: تم تحسين آلية حساب مؤشر Ichimoku Cloud في ملف `user_market_data.py` لضمان ظهوره بشكل صحيح في التحليل.
- **إضافة مؤشر Ichimoku Cloud إلى بيانات السوق**: تم إضافة قيم مؤشر Ichimoku Cloud إلى بيانات السوق عند استخدام API المستخدم.
- **تحسين إنشاء الرسم البياني**: تم تحسين آلية إنشاء الرسم البياني لإظهار مؤشر Ichimoku Cloud بشكل صحيح عند استخدام API المستخدم.
- **تحسين تجربة المستخدم**: تحسين تجربة المستخدم من خلال عرض مؤشر Ichimoku Cloud بشكل متسق سواء عند استخدام API العام أو API المستخدم.

## [2024-08-13] - إصلاح مؤشر Ichimoku Cloud والميزات المتقدمة

- **إصلاح مؤشر Ichimoku Cloud**: تم إصلاح مشكلة عرض مؤشر Ichimoku Cloud في الرسم البياني بإضافة عرض السحابة بشكل صحيح.
- **تحديث نموذج Gemini**: تم تحديث النموذج المستخدم من `gemini-1.5-flash` إلى `gemini-2.0-flash` مع آلية للرجوع إلى النموذج السابق في حالة الفشل.
- **إصلاح مشكلة ذكر عملة واحدة**: تم إصلاح مشكلة ظهور رمز العملة كاملاً (مثل BTCUSDT) في التحليل بدلاً من العملة الأساسية فقط (BTC).
- **تحسين عرض مؤشر Ichimoku Cloud**: تم تحسين طريقة عرض مؤشر Ichimoku Cloud في الرسم البياني بإضافة منطقة السحابة الملونة.
- **تحسين تجربة المستخدم**: تحسين تجربة المستخدم من خلال عرض المؤشرات الفنية بشكل أكثر وضوحاً ودقة.
- **تحسين الميزات المتقدمة**: تم إصلاح الميزات المتقدمة الثلاث (استراتيجية تداول، تنبؤات سعرية، تحليل متعدد الإطارات) للمستخدمين المشتركين.

## [2024-08-12] - تحسين معالجة استجابات Gemini القصيرة

- **إصلاح مشكلة الاستجابات القصيرة**: تم إصلاح مشكلة الاستجابات القصيرة جدًا من نموذج Gemini (أقل من 100 حرف) التي كانت تظهر للمستخدمين.
- **إضافة آلية إعادة المحاولة**: تم إضافة آلية لإعادة المحاولة مع توجيهات إضافية عندما تكون استجابة Gemini قصيرة جدًا.
- **تحسين التوجيهات**: تم تحسين التوجيهات المرسلة إلى نموذج Gemini للحصول على استجابات أكثر تفصيلاً وشمولية.
- **تحسين تسجيل الأخطاء**: تم إضافة تسجيل مفصل لطول الاستجابات ومحاولات إعادة الاستدعاء.
- **تحسين تجربة المستخدم**: تحسين تجربة المستخدم من خلال ضمان الحصول على استجابات مفيدة ومفصلة من الذكاء الاصطناعي.
- **تطبيق التحسينات على جميع وحدات Gemini**: تم تطبيق التحسينات على جميع وحدات استخدام Gemini (ai_chat.py, gemini_analysis.py, trading_education.py).

## [2024-08-11] - إصلاح مشكلة تعلم التداول بالذكاء الاصطناعي

- **إصلاح مشكلة معلمة preselect_platform**: تم إصلاح خطأ "api_setup_command() got an unexpected keyword argument 'preselect_platform'" الذي كان يظهر عند محاولة استخدام ميزة تعلم التداول بالذكاء الاصطناعي للمستخدمين الذين لم يضيفوا مفتاح Gemini API.
- **تحسين دالة api_setup_command**: تم تعديل دالة api_setup_command لتقبل معلمة اختيارية preselect_platform لتوجيه المستخدم مباشرة إلى صفحة إعداد المنصة المحددة.
- **تحسين تجربة المستخدم**: تحسين تجربة المستخدم عند استخدام ميزة تعلم التداول بالذكاء الاصطناعي من خلال توجيهه مباشرة إلى صفحة إعداد مفتاح Gemini API إذا لم يكن لديه مفتاح.
- **تحديث وثائق المشروع**: تم تحديث وثائق المشروع لتعكس التغييرات في دالة api_setup_command.

## [2024-08-10] - إضافة مؤشر Ichimoku Cloud في تحليل الذكاء الاصطناعي

- **تحسين تحليل الذكاء الاصطناعي**: تم إضافة مؤشر Ichimoku Cloud (سحابة إيشيموكو) في تحليل الذكاء الاصطناعي للمستخدمين المشتركين.
- **تحسين تحليل الاتجاهات**: تم تحسين تحليل الاتجاهات باستخدام مؤشر Ichimoku Cloud لتوفير رؤية أكثر شمولية.
- **إضافة خيار نوع التحليل**: تم إضافة خيار للمستخدمين المشتركين للاختيار بين التحليل التقليدي وتحليل الذكاء الاصطناعي.
- **تحديث وثائق المشروع**: تم تحديث وثائق المشروع لتعكس إضافة مؤشر Ichimoku Cloud في تحليل الذكاء الاصطناعي.

## [2024-08-08] - إضافة مؤشر Ichimoku Cloud كميزة مدفوعة

- **إضافة مؤشر جديد**: تم إضافة مؤشر Ichimoku Cloud (سحابة إيشيموكو) كميزة مدفوعة للمستخدمين المشتركين.
- **تحسين التحليل التقليدي**: تم تحديث التحليل التقليدي لإظهار تحليل مؤشر Ichimoku Cloud للمستخدمين المشتركين.
- **تحسين الرسم البياني**: تم إضافة مؤشر Ichimoku Cloud في الرسم البياني للمستخدمين المشتركين.
- **تحسين إدارة حالة الاشتراك**: تم إضافة حالة الاشتراك إلى بيانات السوق لاستخدامها في التحليل.

## [2024-08-07] - إضافة أمر مطور جديد لمنح يوم مجاني

- **إضافة أمر مطور جديد:** تم إضافة أمر مطور جديد `/free_day` لمنح المستخدمين يوم مجاني لاستخدام المميزات المدفوعة.
- **تحسين نظام اليوم المجاني:** تم تحسين نظام اليوم المجاني بإضافة دالة `is_free_day_active` للتحقق من حالة اليوم المجاني.
- **تحديث نظام الاشتراكات:** تم تحديث نظام الاشتراكات لاستخدام الدالة الجديدة للتحقق من حالة اليوم المجاني.
- **تحديث وثائق أوامر المطور:** تم تحديث وثائق أوامر المطور لتشمل الأمر الجديد.

## [2024-08-06] - تحسين واجهة إعداد API وإضافة روابط مباشرة

- **إضافة روابط مباشرة:** تم إضافة روابط مباشرة إلى صفحات إدارة API في المنصات المختلفة لتسهيل الوصول إليها.
- **تحسين عرض المعلومات:** تم تحسين تنسيق النص باستخدام وسوم HTML مثل `<b>` و `<a>` لتحسين تجربة المستخدم.
- **إصلاح مشكلة عدم ظهور واجهة إعداد API:** تم إصلاح مشكلة عدم ظهور واجهة إعداد API عند النقر على زر "إعداد API".
- **تحسين تسجيل المعلومات للتصحيح:** تم إضافة تسجيل مفصل في دالة `get_api_info` لتسهيل تشخيص المشكلات.
- **تحسين معالجة الأخطاء:** تم تحسين معالجة الأخطاء في تحويل التواريخ وتحسين التعامل مع الحالات الاستثنائية.

## [2024-08-05] - إصلاح مشكلة متغير 'instructions' في واجهة إعداد API

- **إصلاح مشكلة متغير 'instructions':** تم إصلاح مشكلة في واجهة إعداد API حيث كان يظهر خطأ "cannot access local variable 'instructions' where it is not associated with a value" عند اختيار منصة تداول جديدة.
- **إضافة تعليمات لجميع المنصات المدعومة:** تم إضافة تعليمات لجميع المنصات المدعومة (KuCoin, Coinbase, Bybit, OKX, Kraken) في دالة `show_api_instructions`.
- **تحسين توثيق الدالة:** تم تحديث توثيق دالة `show_api_instructions` لتعكس جميع المنصات المدعومة.
- **تحسين تجربة المستخدم:** تم تحسين تجربة المستخدم من خلال توفير تعليمات واضحة لكيفية الحصول على مفاتيح API لكل منصة.

## [2024-08-04] - إصلاح مشكلة واجهة إعداد API

- **إصلاح مشكلة واجهة إعداد API:** تم إصلاح مشكلة في واجهة إعداد API حيث لم تكن الرسالة تظهر بعد النقر على زر "إعداد API".
- **تحسين معالجة الأخطاء:** تم إضافة معالجة أخطاء شاملة لجميع دوال واجهة API لضمان عدم توقف البوت في حالة حدوث خطأ.
- **تحسين استقرار البوت:** تم تحسين استقرار البوت من خلال معالجة الأخطاء بشكل أفضل وتوفير رسائل خطأ أكثر وضوحًا.
- **تحسين تجربة المستخدم:** تم تحسين تجربة المستخدم من خلال إضافة رسائل خطأ مفيدة في حالة فشل عمليات إعداد API.

## [2024-08-03] - إضافة دعم منصات تداول متعددة

- **إضافة دعم لمنصات تداول جديدة:** تم إضافة دعم للمنصات التالية: KuCoin، Coinbase، Bybit، OKX، Kraken.
- **تحسين واجهة إعداد API:** تم تطوير واجهة جديدة لاختيار منصة التداول قبل إضافة مفاتيح API.
- **تحديث الرسالة الترحيبية:** تم تحديث الرسالة الترحيبية لإظهار المنصات المختارة.
- **تحسين إدارة مفاتيح API:** تم تحسين نظام إدارة مفاتيح API للتعامل مع منصات متعددة.
- **تبسيط واجهة المستخدم:** تم تبسيط واجهة المستخدم لإدارة مفاتيح API المتعددة.

## [2024-08-02] - إصلاح مشاكل تهيئة Firebase ووحدة الدردشة مع الذكاء الاصطناعي

- **إضافة وحدة تهيئة Firebase:** تم إنشاء ملف `firebase_init.py` جديد لتهيئة Firebase بشكل آمن ومنفصل.
- **تحسين تهيئة Firebase:** تم تعديل الملفات الرئيسية لاستخدام وحدة تهيئة Firebase الجديدة مع آلية للتعامل مع الأخطاء.
- **إصلاح مشكلة وحدة الدردشة مع الذكاء الاصطناعي:** تم إصلاح مشكلة استدعاء وحدة `commodities_data` المفقودة في ملف `ai_chat.py`.
- **إصلاح مشكلة استدعاء `time`:** تم إصلاح مشكلة استدعاء `time` في `job_queue.run_daily` باستخدام `datetime_time`.
- **تحسين استقرار البوت:** تم تحسين استقرار البوت من خلال معالجة الأخطاء بشكل أفضل وتوفير رسائل خطأ أكثر وضوحًا.

## [2024-08-01] - إزالة تحليل السلع

- **إزالة ملف `commodities_data.py`:** تم حذف ملف `commodities_data.py` بالكامل من المشروع.
- **إزالة جميع الإشارات إلى السلع:** تم إزالة جميع الإشارات إلى تحليل السلع من جميع ملفات المشروع.
- **إزالة دعم Alpha Vantage API:** تم إزالة جميع الدوال والمتغيرات المتعلقة بـ Alpha Vantage API.
- **تحديث نصوص المساعدة:** تم تحديث نصوص المساعدة لإزالة أي إشارات إلى تحليل السلع.
- **تبسيط واجهة المستخدم:** تم تبسيط واجهة المستخدم بإزالة أزرار وخيارات تحليل السلع.
- **تحسين التركيز:** تركيز البوت الآن فقط على تحليل العملات الرقمية.

## [2024-08-15] - إصلاح مؤشر Ichimoku Cloud في الميزات المتقدمة

- **إصلاح مؤشر Ichimoku Cloud في الميزات المتقدمة**: تم إصلاح مشكلة عدم ظهور مؤشر Ichimoku Cloud في الميزات المتقدمة (تنبؤات سعرية، استراتيجية تداول، تحليل متعدد الإطارات).
- **إضافة دالة get_ichimoku_analysis**: تم إضافة دالة `get_ichimoku_analysis` في ملف gemini_analysis.py لتحليل مؤشر إيشيموكو باستخدام نموذج Gemini.
- **تحسين معالجة بيانات إيشيموكو**: تم تحسين معالجة بيانات مؤشر إيشيموكو في الميزات المتقدمة لضمان ظهوره بشكل صحيح.
- **تحسين تجربة المستخدم**: تحسين تجربة المستخدم من خلال توفير تحليل شامل لمؤشر إيشيموكو في جميع الميزات المتقدمة.

# سجل التغييرات
## الإصدار 1.3.9 (2024-07-30)

### تحسينات جذرية لتحليل المعادن الثمينة
- **إضافة مصدر بيانات جديد**: تم إضافة دعم لـ API خارجي (metals.live) للحصول على أسعار الذهب والفضة الحقيقية بدلاً من الاعتماد على ETFs.
- **تحسين دقة نسبة التغير**: تم تحسين آلية حساب نسبة التغير اليومية باستخدام بيانات تاريخية من المصدر الخارجي.
- **تنفيذ نظام احتياطي**: تم تنفيذ نظام يستخدم المصدر الخارجي أولاً، ثم ينتقل إلى Alpha Vantage كمصدر احتياطي في حالة الفشل.
- **تحسين تسجيل العمليات**: تم إضافة تسجيل تفصيلي لعمليات الحصول على البيانات من المصادر المختلفة.

## الإصدار 1.3.8 (2024-07-29)

### تحسينات إضافية لتحليل السلع
- **تحسين معامل تحويل الذهب**: تم إضافة آلية ديناميكية لتعديل معامل تحويل سعر ETF GLD إلى سعر الذهب الحقيقي بناءً على السعر الحالي.
- **تحسين آلية تجنب التخزين المؤقت**: تم تحسين آلية تجنب التخزين المؤقت عند استدعاء Alpha Vantage API باستخدام طابع زمني بالميلي ثانية.
- **تحسين تسجيل الطلبات**: تم إضافة تسجيل إضافي لطلبات API وتحليل الاتجاهات لتسهيل تتبع المشكلات.
- **إجبار تحديث البيانات**: تم التأكد من إجبار تحديث البيانات في كل مرة يتم فيها طلب تحليل السلع.
- **تحسين تحليل الاتجاهات**: تم تحسين تحليل الاتجاهات (طويل، متوسط، قصير المدى) مع إضافة تسجيل تفصيلي للقرارات.

## الإصدار 1.3.7 (2024-07-28)

### تحسينات تحليل السلع
- **إصلاح مشكلة تحديث أسعار المعادن الثمينة**: تم إصلاح مشكلة عدم تحديث أسعار الذهب والفضة عند استخدام مفتاح Alpha Vantage API.
- **تحسين استخدام Alpha Vantage API**: تم تعديل النظام لاستخدام Alpha Vantage TIME_SERIES API مع ETFs للمعادن الثمينة (GLD للذهب، SLV للفضة) بدلاً من البيانات الثابتة.
- **إصلاح خطأ استدعاء API**: تم إصلاح خطأ "Invalid API call" عند محاولة استخدام FOREX API للمعادن الثمينة.
- **تحسين معالجة البيانات**: تم تبسيط معالجة بيانات Alpha Vantage للتعامل مع TIME_SERIES API بشكل موحد لجميع السلع.
- **تحسين تسجيل الأخطاء**: تم إضافة تسجيل أكثر تفصيلاً للأخطاء لتسهيل تتبع المشكلات المتعلقة بتحليل السلع.
- **إضافة تنبيه إلزامي لمفتاح API**: تم إضافة تنبيه إلزامي للمستخدمين الذين يطلبون تحليل السلع ولم يضيفوا مفتاح Alpha Vantage API، مع زر مباشر لإضافة المفتاح.
- **تحويل أسعار ETFs إلى أسعار حقيقية**: تم إضافة آلية لتحويل أسعار ETFs (GLD و SLV) إلى أسعار الذهب والفضة الحقيقية.
- **تحسين دقة معاملات التحويل**: تم ضبط معاملات التحويل بدقة أكبر (10.7 للذهب و1.5 للفضة) بناءً على الأسعار الحالية.
- **تحويل جميع البيانات التاريخية**: تم تحسين آلية التحويل لتطبيق معامل التحويل على جميع البيانات التاريخية وإعادة حساب نسب التغير والمؤشرات الفنية.
- **تحسين تحليل الاتجاه**: تم تعديل خوارزمية تحليل الاتجاه لتأخذ في الاعتبار نسبة التغير اليومية بالإضافة إلى المتوسطات المتحركة.
- **إجبار تحديث البيانات**: تم إضافة آلية لإجبار تحديث البيانات في كل مرة يتم فيها طلب تحليل السلع.

## الإصدار 1.3.6 (2024-07-25)

### تحسينات البنية التحتية
- **تحسين مجدول تشغيل رابط فحص الصحة**: تم تعديل مجدول تشغيل رابط فحص الصحة ليعمل كل دقيقة بدلاً من كل 3 دقائق.
- **إضافة آلية للمحاولة المتكررة**: تم إضافة آلية للمحاولة المتكررة في حالة فشل تشغيل رابط فحص الصحة.
- **تحسين استقرار الخدمة**: تحسين استقرار الخدمة من خلال زيادة تكرار فحص الصحة.

## الإصدار 1.3.5 (2024-07-20)

### تحسينات نظام الدفع
- **إضافة إشعارات المعاملات المنتهية**: تم إضافة نظام لإرسال إشعارات للمستخدمين قبل انتهاء صلاحية معاملاتهم.
- **إضافة خيار تمديد صلاحية المعاملة**: تم إضافة خيار صريح للمستخدم لتمديد صلاحية المعاملة المعلقة.
- **تحسين واجهة المستخدم للدفع**: تحسين واجهة المستخدم لعملية الدفع وإضافة أزرار للتحكم في المعاملات المعلقة.
- **تحسين نظام التحقق من الدفع**: تحسين نظام التحقق من الدفع وإضافة خيار للتحقق اليدوي من حالة الدفع.

## الإصدار 1.3.4 (2024-07-17)

### إصلاحات
- **إصلاح خطأ في القائمة الرئيسية**: تم إصلاح خطأ كان يمنع عرض القائمة الرئيسية بسبب مشكلة في الوصول إلى متغير `has_alpha_vantage_api`.
- **إصلاح مشكلة عدم تحديث حالة Alpha Vantage API**: تم إصلاح مشكلة كانت تمنع تحديث حالة Alpha Vantage API من ❌ إلى ✅ بعد إضافة المفتاح.

## الإصدار 1.3.3 (2024-07-17)

### تحسينات
- **إصلاح مشكلة إضافة Alpha Vantage API للمستخدمين غير المشتركين**: تم إصلاح مشكلة كانت تمنع المستخدمين غير المشتركين من إضافة مفتاح Alpha Vantage API.
- **تحسين واجهة المستخدم**: تم إضافة علامة ✅ أو ❌ بجانب Binance API في الرسالة الترحيبية لإظهار حالة الاتصال.
- **تحسين الرسالة الترحيبية**: تم تحديث الرسالة الترحيبية لتشجيع جميع المستخدمين على إضافة مفتاح Alpha Vantage API.

## الإصدار 1.3.2 (2024-07-16)

### تحسينات
- **إتاحة إضافة مفتاح Alpha Vantage API للمستخدمين غير المشتركين**: تم تعديل النظام للسماح لجميع المستخدمين بإضافة مفتاح Alpha Vantage API الخاص بهم.
- **تحسين تحليل السلع**: تم تعديل نظام تحليل السلع لاستخدام مفتاح API الخاص بالمستخدم إذا كان متاحاً، مع الحفاظ على حد 3 تحليلات يومياً للمستخدمين غير المشتركين.

## الإصدار 1.3.1 (2024-07-15)

### إصلاحات الأخطاء
- **إصلاح مشكلة تحليل العملات والسلع**: تم إصلاح خطأ `asyncio.run() cannot be called from a running event loop` الذي كان يظهر عند تحليل العملات والسلع.
- **تحسين معالجة الدوال المتزامنة**: تحديث طريقة استدعاء الدوال المتزامنة في ملف `main.py` لتجنب تداخل حلقات الأحداث.

## الإصدار 1.3.0 (2024-07-10)

### تحسينات نظام الدفع
- **تحسين نظام تنظيف المعاملات**: تم تنفيذ نظام متكامل لتنظيف المعاملات المعلقة وإدارتها بشكل أفضل.
- **تمديد صلاحية المعاملات**: تم إضافة آلية لتمديد صلاحية المعاملات المعلقة تلقائيًا عند محاولة إنشاء معاملة جديدة.
- **تحسين إدارة المعاملات**: تحسين آلية التعامل مع المعاملات المنتهية وحذف المعاملات القديمة.

## الإصدار 1.2.3 (2024-07-05)

### إصلاحات الأخطاء
- **إصلاح مشكلة اللغة في تحليل السلع**: إصلاح مشكلة خلط اللغات في واجهة تحليل السلع، حيث كانت بعض النصوص تظهر باللغة العربية حتى عند اختيار اللغة الإنجليزية.
- **تحسين دعم اللغات**: تحديث دالة تحليل المؤشرات الفنية للسلع لدعم اللغتين العربية والإنجليزية بشكل كامل.

## الإصدار 1.2.2 (2024-07-05)

### تحسينات واجهة المستخدم
- **تحديث الرسالة الترحيبية**: تحديث الرسالة الترحيبية باللغتين العربية والإنجليزية لتشمل معلومات عن تحليل السلع.
- **تحسين قائمة المميزات**: تحديث قائمة المميزات في الرسالة الترحيبية لتوضيح دعم تحليل السلع.
- **تحديث نص المساعدة**: تحديث نص المساعدة باللغتين العربية والإنجليزية ليشمل معلومات عن تحليل السلع.

## الإصدار 1.2.1 (2024-06-28)

### تحسينات البنية التحتية
- **تحسين استقرار النشر**: تحسين ملف main_wrapper.py لضمان استمرارية عمل البوت على منصة Koyeb.
- **إضافة مجدول لتشغيل رابط فحص الصحة**: إضافة مهمة مجدولة لتشغيل رابط فحص الصحة كل 3 دقائق للحفاظ على استمرارية الخدمة.
- **تحسين معالجة الأخطاء**: تحسين معالجة الأخطاء في حالة فشل تشغيل البوت الرئيسي.
- **إضافة مكتبة schedule**: إضافة مكتبة schedule لإدارة المهام المجدولة بشكل أفضل.

## الإصدار 1.2.0 (2024-06-15)

### ميزات جديدة
- **تحليل السلع**: إضافة دعم لتحليل السلع الأساسية (الذهب، الفضة، النفط) لجميع المستخدمين.
- **تحسين التعرف على الطلبات**: إضافة دعم للتعرف على طلبات تحليل السلع باللغة العربية (مثل "تحليل الذهب").
- **تحسين تنظيف رموز العملات والسلع**: تحسين دالة `clean_symbol` للتعرف على رموز السلع وتوحيدها.

### تحسينات
- **إتاحة تحليل السلع للجميع**: تم إتاحة ميزة تحليل السلع الأساسية (الذهب والفضة) لجميع المستخدمين مع حد 3 تحليلات يومياً للمستخدمين غير المشتركين.
- **تحسين واجهة المستخدم**: إضافة زر تحليل السلع في القائمة الرئيسية لجميع المستخدمين وتوضيح الفرق بين أنواع النفط (WTI وبرنت).
- **تحسين التحليل الفني للسلع**: إضافة تحليل فني أساسي للسلع يشمل المؤشرات الفنية الرئيسية.
- **إضافة عداد للتحليلات المتبقية**: إظهار عدد التحليلات المجانية المتبقية للمستخدمين غير المشتركين.

## الإصدار 1.1.0 (2024-06-01)

### تحسينات الأداء
- **إزالة الاعتماد على Redis**: تم استبدال Redis بنظام تخزين مؤقت محلي في الذاكرة ونظام تخزين مؤقت باستخدام Firestore للبيانات المهمة.
- **تحسين أداء تحليل العملات**: تقليل تكرار حساب المؤشرات الفنية وتخزينها في الذاكرة المحلية.
- **تجميع طلبات بيانات السوق**: تحسين طريقة جلب بيانات السوق وتقليل عدد الطلبات.
- **تحسين نظام الاشتراكات**: تحديث نظام الاشتراكات لاستخدام الذاكرة المحلية بدلاً من Redis.

### تحسينات البنية
- **إضافة نظام تخزين مؤقت باستخدام Firestore**: تم إنشاء فئة `FirestoreCache` لتوفير واجهة بسيطة للتخزين المؤقت باستخدام Firestore.
- **تحديث الرسالة الترحيبية**: تم تحديث الرسالة الترحيبية لتعكس التغييرات الجديدة.
- **إضافة مهمة تنظيف البيانات المؤقتة**: تم إضافة مهمة مجدولة لتنظيف البيانات المؤقتة منتهية الصلاحية.
- **تحديث معلومات النظام**: تم تحديث دالة `system_info` لعرض إحصائيات التخزين المؤقت.

### تحسينات التكلفة
- **تقليل الاعتماد على التخزين المؤقت**: تم تقليل الاعتماد على التخزين المؤقت واستخدام Firestore للبيانات المؤقتة الضرورية فقط.
- **إزالة الحاجة لخادم Redis منفصل**: تم إزالة الحاجة لخادم Redis منفصل وتقليل تكلفة البنية التحتية.

## الإصدار 1.0.0 (2024-01-01)

### المميزات الأولية
- **تحليل العملات الرقمية**: تحليل العملات الرقمية باستخدام المؤشرات الفنية.
- **إعداد تنبيهات سعرية**: إمكانية إعداد تنبيهات عند وصول سعر العملة إلى قيمة معينة.
- **نظام اشتراكات متكامل**: نظام اشتراكات متكامل مع دعم الدفع عبر PayPal.
- **دعم اللغتين العربية والإنجليزية**: واجهة مستخدم ثنائية اللغة.
- **واجهة سهلة الاستخدام**: واجهة سهلة الاستخدم عبر تطبيق تلغرام.
