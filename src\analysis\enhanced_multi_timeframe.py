"""
نظام التحليل متعدد الإطارات الزمنية المحسن
Enhanced Multi-Timeframe Analysis System

هذا الملف يحتوي على نظام تحليل متطور يستخدم:
1. التحليل الهرمي المتكامل (من الأعلى للأسفل)
2. مؤشرات متخصصة لكل إطار زمني
3. نظام تأكيد الإشارات متعدد المستويات
4. استراتيجيات مخصصة لأنماط التداول
5. تقييم المخاطر متعدد الأبعاد
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Any, List, Tuple, Optional, Union
from enum import Enum
import asyncio
import json

# إعداد السجل
logger = logging.getLogger(__name__)

class TradingStyle(Enum):
    """أنماط التداول المختلفة"""
    SCALPING = "scalping"
    DAY_TRADING = "day_trading"
    SWING_TRADING = "swing_trading"
    POSITION = "position"

class MarketCondition(Enum):
    """حالات السوق"""
    TRENDING_UP = "trending_up"
    TRENDING_DOWN = "trending_down"
    SIDEWAYS = "sideways"
    VOLATILE = "volatile"
    LOW_VOLATILITY = "low_volatility"

class SignalStrength(Enum):
    """قوة الإشارة"""
    VERY_STRONG = 5
    STRONG = 4
    MODERATE = 3
    WEAK = 2
    VERY_WEAK = 1

class TimeframeConfig:
    """تكوين الإطارات الزمنية المتدرجة"""
    
    # إطارات زمنية متدرجة ومترابطة
    SMART_TIMEFRAMES = {
        TradingStyle.SCALPING: ['1m', '5m', '15m'],
        TradingStyle.DAY_TRADING: ['15m', '1h', '4h'],
        TradingStyle.SWING_TRADING: ['4h', '1d', '1w'],
        TradingStyle.POSITION: ['1d', '1w', '1M']
    }
    
    # مؤشرات متخصصة حسب الإطار الزمني
    TIMEFRAME_INDICATORS = {
        'short_term': {
            'primary': ['rsi', 'stoch_k', 'stoch_d', 'williams_r', 'cci'],
            'secondary': ['bb_upper', 'bb_lower', 'bb_middle'],
            'volume': ['volume_sma', 'volume_ratio']
        },
        'medium_term': {
            'primary': ['macd', 'macd_signal', 'ema20', 'ema50', 'adx'],
            'secondary': ['plus_di', 'minus_di', 'atr'],
            'trend': ['parabolic_sar', 'trend_strength']
        },
        'long_term': {
            'primary': ['ichimoku_tenkan', 'ichimoku_kijun', 'ichimoku_senkou_a', 'ichimoku_senkou_b'],
            'secondary': ['sma50', 'sma200', 'ema100'],
            'structure': ['pivot_points', 'fibonacci_levels']
        }
    }
    
    # أوزان الإطارات الزمنية حسب نمط التداول
    TIMEFRAME_WEIGHTS = {
        TradingStyle.SCALPING: {'1m': 0.5, '5m': 0.3, '15m': 0.2},
        TradingStyle.DAY_TRADING: {'15m': 0.2, '1h': 0.5, '4h': 0.3},
        TradingStyle.SWING_TRADING: {'4h': 0.2, '1d': 0.5, '1w': 0.3},
        TradingStyle.POSITION: {'1d': 0.2, '1w': 0.5, '1M': 0.3}
    }

class AdvancedIndicators:
    """حساب المؤشرات الفنية المتقدمة"""
    
    @staticmethod
    def calculate_williams_r(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """حساب مؤشر Williams %R"""
        try:
            highest_high = high.rolling(window=period).max()
            lowest_low = low.rolling(window=period).min()
            williams_r = -100 * (highest_high - close) / (highest_high - lowest_low)
            return williams_r
        except Exception as e:
            logger.error(f"خطأ في حساب Williams %R: {str(e)}")
            return pd.Series([np.nan] * len(close))
    
    @staticmethod
    def calculate_cci(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 20) -> pd.Series:
        """حساب مؤشر Commodity Channel Index"""
        try:
            typical_price = (high + low + close) / 3
            sma_tp = typical_price.rolling(window=period).mean()
            mad = typical_price.rolling(window=period).apply(lambda x: np.mean(np.abs(x - x.mean())))
            cci = (typical_price - sma_tp) / (0.015 * mad)
            return cci
        except Exception as e:
            logger.error(f"خطأ في حساب CCI: {str(e)}")
            return pd.Series([np.nan] * len(close))
    
    @staticmethod
    def calculate_atr(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """حساب مؤشر Average True Range"""
        try:
            tr1 = high - low
            tr2 = abs(high - close.shift(1))
            tr3 = abs(low - close.shift(1))
            true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            atr = true_range.rolling(window=period).mean()
            return atr
        except Exception as e:
            logger.error(f"خطأ في حساب ATR: {str(e)}")
            return pd.Series([np.nan] * len(close))
    
    @staticmethod
    def calculate_parabolic_sar(high: pd.Series, low: pd.Series, close: pd.Series, 
                               af_start: float = 0.02, af_increment: float = 0.02, af_max: float = 0.2) -> pd.Series:
        """حساب مؤشر Parabolic SAR"""
        try:
            length = len(close)
            sar = pd.Series(index=close.index, dtype=float)
            trend = pd.Series(index=close.index, dtype=int)
            af = pd.Series(index=close.index, dtype=float)
            ep = pd.Series(index=close.index, dtype=float)
            
            # تهيئة القيم الأولى
            sar.iloc[0] = low.iloc[0]
            trend.iloc[0] = 1  # 1 للاتجاه الصاعد، -1 للاتجاه الهابط
            af.iloc[0] = af_start
            ep.iloc[0] = high.iloc[0]
            
            for i in range(1, length):
                if trend.iloc[i-1] == 1:  # اتجاه صاعد
                    sar.iloc[i] = sar.iloc[i-1] + af.iloc[i-1] * (ep.iloc[i-1] - sar.iloc[i-1])
                    
                    if low.iloc[i] <= sar.iloc[i]:
                        trend.iloc[i] = -1
                        sar.iloc[i] = ep.iloc[i-1]
                        ep.iloc[i] = low.iloc[i]
                        af.iloc[i] = af_start
                    else:
                        trend.iloc[i] = 1
                        if high.iloc[i] > ep.iloc[i-1]:
                            ep.iloc[i] = high.iloc[i]
                            af.iloc[i] = min(af.iloc[i-1] + af_increment, af_max)
                        else:
                            ep.iloc[i] = ep.iloc[i-1]
                            af.iloc[i] = af.iloc[i-1]
                else:  # اتجاه هابط
                    sar.iloc[i] = sar.iloc[i-1] + af.iloc[i-1] * (ep.iloc[i-1] - sar.iloc[i-1])
                    
                    if high.iloc[i] >= sar.iloc[i]:
                        trend.iloc[i] = 1
                        sar.iloc[i] = ep.iloc[i-1]
                        ep.iloc[i] = high.iloc[i]
                        af.iloc[i] = af_start
                    else:
                        trend.iloc[i] = -1
                        if low.iloc[i] < ep.iloc[i-1]:
                            ep.iloc[i] = low.iloc[i]
                            af.iloc[i] = min(af.iloc[i-1] + af_increment, af_max)
                        else:
                            ep.iloc[i] = ep.iloc[i-1]
                            af.iloc[i] = af.iloc[i-1]
            
            return sar
        except Exception as e:
            logger.error(f"خطأ في حساب Parabolic SAR: {str(e)}")
            return pd.Series([np.nan] * len(close))
    
    @staticmethod
    def calculate_trend_strength(close: pd.Series, period: int = 20) -> pd.Series:
        """حساب قوة الاتجاه"""
        try:
            # حساب المتوسط المتحرك
            sma = close.rolling(window=period).mean()
            
            # حساب انحراف السعر عن المتوسط
            deviation = (close - sma) / sma * 100
            
            # حساب قوة الاتجاه بناءً على الانحراف والاتساق
            trend_strength = deviation.rolling(window=period).apply(
                lambda x: np.mean(x) * (1 - np.std(x) / (np.abs(np.mean(x)) + 1))
            )
            
            return trend_strength
        except Exception as e:
            logger.error(f"خطأ في حساب قوة الاتجاه: {str(e)}")
            return pd.Series([np.nan] * len(close))
    
    @staticmethod
    def calculate_volume_indicators(volume: pd.Series, close: pd.Series, period: int = 20) -> Dict[str, pd.Series]:
        """حساب مؤشرات الحجم"""
        try:
            volume_sma = volume.rolling(window=period).mean()
            volume_ratio = volume / volume_sma
            
            # حساب مؤشر On-Balance Volume
            obv = pd.Series(index=close.index, dtype=float)
            obv.iloc[0] = volume.iloc[0]
            
            for i in range(1, len(close)):
                if close.iloc[i] > close.iloc[i-1]:
                    obv.iloc[i] = obv.iloc[i-1] + volume.iloc[i]
                elif close.iloc[i] < close.iloc[i-1]:
                    obv.iloc[i] = obv.iloc[i-1] - volume.iloc[i]
                else:
                    obv.iloc[i] = obv.iloc[i-1]
            
            return {
                'volume_sma': volume_sma,
                'volume_ratio': volume_ratio,
                'obv': obv
            }
        except Exception as e:
            logger.error(f"خطأ في حساب مؤشرات الحجم: {str(e)}")
            return {
                'volume_sma': pd.Series([np.nan] * len(volume)),
                'volume_ratio': pd.Series([np.nan] * len(volume)),
                'obv': pd.Series([np.nan] * len(volume))
            }

class HierarchicalAnalysis:
    """نظام التحليل الهرمي المتكامل"""

    def __init__(self, trading_style: TradingStyle = TradingStyle.DAY_TRADING):
        self.trading_style = trading_style
        self.timeframes = TimeframeConfig.SMART_TIMEFRAMES[trading_style]
        self.weights = TimeframeConfig.TIMEFRAME_WEIGHTS[trading_style]
        self.advanced_indicators = AdvancedIndicators()

    def analyze_top_down(self, timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """تحليل هرمي من الأعلى للأسفل"""
        try:
            # 1. تحليل الاتجاه العام (إطارات طويلة)
            long_term_analysis = self._analyze_long_term_trend(timeframes_data)

            # 2. تحليل الاتجاه المتوسط
            medium_term_analysis = self._analyze_medium_term_trend(timeframes_data)

            # 3. تحليل نقاط الدخول (إطارات قصيرة)
            short_term_analysis = self._analyze_short_term_signals(timeframes_data)

            # 4. دمج التحليلات
            integrated_analysis = self._synthesize_analysis(
                long_term_analysis,
                medium_term_analysis,
                short_term_analysis
            )

            return integrated_analysis

        except Exception as e:
            logger.error(f"خطأ في التحليل الهرمي: {str(e)}")
            return {}

    def _analyze_long_term_trend(self, timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """تحليل الاتجاه طويل المدى"""
        try:
            # استخدام أطول إطار زمني متاح
            longest_timeframe = self.timeframes[-1]
            data = timeframes_data.get(longest_timeframe, {})

            if not data:
                return {'trend': 'unknown', 'strength': 0, 'confidence': 0}

            # تحليل مؤشرات الاتجاه طويل المدى
            trend_signals = []

            # تحليل Ichimoku Cloud
            ichimoku_signal = self._analyze_ichimoku_trend(data)
            if ichimoku_signal:
                trend_signals.append(ichimoku_signal)

            # تحليل المتوسطات المتحركة طويلة المدى
            ma_signal = self._analyze_moving_averages_trend(data)
            if ma_signal:
                trend_signals.append(ma_signal)

            # تحليل هيكل السعر
            structure_signal = self._analyze_price_structure(data)
            if structure_signal:
                trend_signals.append(structure_signal)

            # دمج الإشارات
            return self._combine_trend_signals(trend_signals, 'long_term')

        except Exception as e:
            logger.error(f"خطأ في تحليل الاتجاه طويل المدى: {str(e)}")
            return {'trend': 'unknown', 'strength': 0, 'confidence': 0}

    def _analyze_medium_term_trend(self, timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """تحليل الاتجاه متوسط المدى"""
        try:
            # استخدام الإطار الزمني المتوسط
            medium_timeframe = self.timeframes[1] if len(self.timeframes) > 1 else self.timeframes[0]
            data = timeframes_data.get(medium_timeframe, {})

            if not data:
                return {'trend': 'unknown', 'strength': 0, 'confidence': 0}

            trend_signals = []

            # تحليل MACD
            macd_signal = self._analyze_macd_trend(data)
            if macd_signal:
                trend_signals.append(macd_signal)

            # تحليل ADX
            adx_signal = self._analyze_adx_trend(data)
            if adx_signal:
                trend_signals.append(adx_signal)

            # تحليل Parabolic SAR
            sar_signal = self._analyze_parabolic_sar_trend(data)
            if sar_signal:
                trend_signals.append(sar_signal)

            return self._combine_trend_signals(trend_signals, 'medium_term')

        except Exception as e:
            logger.error(f"خطأ في تحليل الاتجاه متوسط المدى: {str(e)}")
            return {'trend': 'unknown', 'strength': 0, 'confidence': 0}

    def _analyze_short_term_signals(self, timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """تحليل إشارات قصيرة المدى"""
        try:
            # استخدام أقصر إطار زمني
            short_timeframe = self.timeframes[0]
            data = timeframes_data.get(short_timeframe, {})

            if not data:
                return {'signals': [], 'entry_quality': 0, 'timing_score': 0}

            signals = []

            # تحليل RSI
            rsi_signal = self._analyze_rsi_signals(data)
            if rsi_signal:
                signals.append(rsi_signal)

            # تحليل Stochastic
            stoch_signal = self._analyze_stochastic_signals(data)
            if stoch_signal:
                signals.append(stoch_signal)

            # تحليل Williams %R
            williams_signal = self._analyze_williams_signals(data)
            if williams_signal:
                signals.append(williams_signal)

            # تحليل CCI
            cci_signal = self._analyze_cci_signals(data)
            if cci_signal:
                signals.append(cci_signal)

            # تحليل Bollinger Bands
            bb_signal = self._analyze_bollinger_signals(data)
            if bb_signal:
                signals.append(bb_signal)

            return self._combine_entry_signals(signals)

        except Exception as e:
            logger.error(f"خطأ في تحليل الإشارات قصيرة المدى: {str(e)}")
            return {'signals': [], 'entry_quality': 0, 'timing_score': 0}

    def _synthesize_analysis(self, long_term: Dict[str, Any], medium_term: Dict[str, Any],
                           short_term: Dict[str, Any]) -> Dict[str, Any]:
        """دمج التحليلات من جميع الإطارات الزمنية"""
        try:
            # تحديد التوافق بين الإطارات الزمنية
            trend_alignment = self._calculate_trend_alignment(long_term, medium_term)

            # تقييم جودة نقاط الدخول
            entry_quality = self._evaluate_entry_quality(short_term, trend_alignment)

            # حساب مستوى الثقة الإجمالي
            overall_confidence = self._calculate_overall_confidence(long_term, medium_term, short_term)

            # تحديد التوصية النهائية
            recommendation = self._generate_recommendation(
                long_term, medium_term, short_term, trend_alignment, entry_quality
            )

            return {
                'long_term_trend': long_term,
                'medium_term_trend': medium_term,
                'short_term_signals': short_term,
                'trend_alignment': trend_alignment,
                'entry_quality': entry_quality,
                'overall_confidence': overall_confidence,
                'recommendation': recommendation,
                'trading_style': self.trading_style.value,
                'analysis_timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"خطأ في دمج التحليلات: {str(e)}")
            return {}

    # دوال تحليل المؤشرات المتخصصة
    def _analyze_ichimoku_trend(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """تحليل اتجاه Ichimoku Cloud"""
        try:
            tenkan = data.get('ichimoku_tenkan')
            kijun = data.get('ichimoku_kijun')
            senkou_a = data.get('ichimoku_senkou_a')
            senkou_b = data.get('ichimoku_senkou_b')
            price = data.get('price', 0)

            if not all([tenkan, kijun, senkou_a, senkou_b, price]):
                return None

            # تحديد موقع السعر بالنسبة للسحابة
            cloud_top = max(senkou_a, senkou_b)
            cloud_bottom = min(senkou_a, senkou_b)

            if price > cloud_top:
                cloud_position = "above"
                trend_bias = "bullish"
                strength = 3
            elif price < cloud_bottom:
                cloud_position = "below"
                trend_bias = "bearish"
                strength = 3
            else:
                cloud_position = "inside"
                trend_bias = "neutral"
                strength = 1

            # تحليل تقاطع Tenkan و Kijun
            if tenkan > kijun:
                tk_signal = "bullish"
                strength += 1
            elif tenkan < kijun:
                tk_signal = "bearish"
                strength += 1
            else:
                tk_signal = "neutral"

            # تحليل اتجاه السحابة
            if senkou_a > senkou_b:
                cloud_trend = "bullish"
                strength += 1
            elif senkou_a < senkou_b:
                cloud_trend = "bearish"
                strength += 1
            else:
                cloud_trend = "neutral"

            return {
                'indicator': 'ichimoku',
                'trend': trend_bias,
                'strength': min(strength, 5),
                'confidence': min(strength * 20, 100),
                'details': {
                    'cloud_position': cloud_position,
                    'tk_signal': tk_signal,
                    'cloud_trend': cloud_trend,
                    'cloud_thickness': abs(senkou_a - senkou_b)
                }
            }

        except Exception as e:
            logger.error(f"خطأ في تحليل Ichimoku: {str(e)}")
            return None

    def _analyze_moving_averages_trend(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """تحليل اتجاه المتوسطات المتحركة"""
        try:
            ema20 = data.get('ema20')
            ema50 = data.get('ema50')
            sma50 = data.get('sma50')
            sma200 = data.get('sma200')
            price = data.get('price', 0)

            if not price:
                return None

            signals = []
            strength = 0

            # تحليل موقع السعر بالنسبة للمتوسطات
            if ema20 and price > ema20:
                signals.append('price_above_ema20')
                strength += 1
            elif ema20 and price < ema20:
                signals.append('price_below_ema20')
                strength += 1

            # تحليل ترتيب المتوسطات المتحركة
            if ema20 and ema50:
                if ema20 > ema50:
                    signals.append('ema20_above_ema50')
                    strength += 2
                else:
                    signals.append('ema20_below_ema50')
                    strength += 2

            if sma50 and sma200:
                if sma50 > sma200:
                    signals.append('golden_cross_potential')
                    strength += 3
                else:
                    signals.append('death_cross_potential')
                    strength += 3

            # تحديد الاتجاه العام
            bullish_signals = len([s for s in signals if 'above' in s or 'golden' in s])
            bearish_signals = len([s for s in signals if 'below' in s or 'death' in s])

            if bullish_signals > bearish_signals:
                trend = 'bullish'
            elif bearish_signals > bullish_signals:
                trend = 'bearish'
            else:
                trend = 'neutral'

            return {
                'indicator': 'moving_averages',
                'trend': trend,
                'strength': min(strength, 5),
                'confidence': min(strength * 15, 100),
                'details': {
                    'signals': signals,
                    'bullish_count': bullish_signals,
                    'bearish_count': bearish_signals
                }
            }

        except Exception as e:
            logger.error(f"خطأ في تحليل المتوسطات المتحركة: {str(e)}")
            return None

    def _analyze_price_structure(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """تحليل هيكل السعر"""
        try:
            price = data.get('price', 0)
            high_24h = data.get('high_24h', 0)
            low_24h = data.get('low_24h', 0)

            if not all([price, high_24h, low_24h]):
                return None

            # حساب موقع السعر في النطاق اليومي
            daily_range = high_24h - low_24h
            if daily_range == 0:
                return None

            price_position = (price - low_24h) / daily_range

            # تحديد هيكل السعر
            if price_position > 0.8:
                structure = 'near_high'
                trend_bias = 'bullish'
                strength = 4
            elif price_position > 0.6:
                structure = 'upper_range'
                trend_bias = 'bullish'
                strength = 3
            elif price_position > 0.4:
                structure = 'middle_range'
                trend_bias = 'neutral'
                strength = 2
            elif price_position > 0.2:
                structure = 'lower_range'
                trend_bias = 'bearish'
                strength = 3
            else:
                structure = 'near_low'
                trend_bias = 'bearish'
                strength = 4

            return {
                'indicator': 'price_structure',
                'trend': trend_bias,
                'strength': strength,
                'confidence': strength * 20,
                'details': {
                    'structure': structure,
                    'price_position': price_position,
                    'daily_range': daily_range
                }
            }

        except Exception as e:
            logger.error(f"خطأ في تحليل هيكل السعر: {str(e)}")
            return None

    def _analyze_macd_trend(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """تحليل اتجاه MACD"""
        try:
            macd = data.get('macd')
            macd_signal = data.get('macd_signal')
            macd_histogram = data.get('macd_histogram')

            if not all([macd, macd_signal, macd_histogram]):
                return None

            signals = []
            strength = 0

            # تحليل تقاطع MACD مع خط الإشارة
            if macd > macd_signal:
                signals.append('macd_above_signal')
                strength += 2
                trend_bias = 'bullish'
            else:
                signals.append('macd_below_signal')
                strength += 2
                trend_bias = 'bearish'

            # تحليل موقع MACD بالنسبة للصفر
            if macd > 0:
                signals.append('macd_above_zero')
                strength += 1
            else:
                signals.append('macd_below_zero')
                strength += 1

            # تحليل الهيستوجرام
            if macd_histogram > 0:
                signals.append('histogram_positive')
                strength += 1
            else:
                signals.append('histogram_negative')
                strength += 1

            return {
                'indicator': 'macd',
                'trend': trend_bias,
                'strength': min(strength, 5),
                'confidence': min(strength * 20, 100),
                'details': {
                    'signals': signals,
                    'macd_value': macd,
                    'signal_value': macd_signal,
                    'histogram_value': macd_histogram
                }
            }

        except Exception as e:
            logger.error(f"خطأ في تحليل MACD: {str(e)}")
            return None

    def _analyze_adx_trend(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """تحليل اتجاه ADX"""
        try:
            adx = data.get('adx')
            plus_di = data.get('plus_di')
            minus_di = data.get('minus_di')

            if not all([adx, plus_di, minus_di]):
                return None

            # تحديد قوة الاتجاه بناءً على ADX
            if adx > 50:
                trend_strength = 'very_strong'
                strength = 5
            elif adx > 30:
                trend_strength = 'strong'
                strength = 4
            elif adx > 20:
                trend_strength = 'moderate'
                strength = 3
            else:
                trend_strength = 'weak'
                strength = 2

            # تحديد اتجاه الاتجاه بناءً على DI
            if plus_di > minus_di:
                trend_direction = 'bullish'
            else:
                trend_direction = 'bearish'

            return {
                'indicator': 'adx',
                'trend': trend_direction,
                'strength': strength,
                'confidence': min(adx * 2, 100),
                'details': {
                    'trend_strength': trend_strength,
                    'adx_value': adx,
                    'plus_di': plus_di,
                    'minus_di': minus_di
                }
            }

        except Exception as e:
            logger.error(f"خطأ في تحليل ADX: {str(e)}")
            return None

    def _analyze_parabolic_sar_trend(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """تحليل اتجاه Parabolic SAR"""
        try:
            sar = data.get('parabolic_sar')
            price = data.get('price', 0)

            if not all([sar, price]):
                return None

            # تحديد الاتجاه بناءً على موقع SAR بالنسبة للسعر
            if price > sar:
                trend = 'bullish'
                strength = 3
            else:
                trend = 'bearish'
                strength = 3

            # حساب المسافة بين السعر و SAR
            distance = abs(price - sar) / price * 100

            # تعديل القوة بناءً على المسافة
            if distance > 5:
                strength += 1
            elif distance < 1:
                strength -= 1

            return {
                'indicator': 'parabolic_sar',
                'trend': trend,
                'strength': max(min(strength, 5), 1),
                'confidence': min(distance * 20, 100),
                'details': {
                    'sar_value': sar,
                    'price_sar_distance': distance
                }
            }

        except Exception as e:
            logger.error(f"خطأ في تحليل Parabolic SAR: {str(e)}")
            return None

    # دوال تحليل المؤشرات قصيرة المدى
    def _analyze_rsi_signals(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """تحليل إشارات RSI"""
        try:
            rsi = data.get('rsi')
            if not rsi:
                return None

            # تحديد حالة RSI
            if rsi >= 80:
                signal_type = 'extremely_overbought'
                trend = 'bearish'
                strength = 5
            elif rsi >= 70:
                signal_type = 'overbought'
                trend = 'bearish'
                strength = 4
            elif rsi >= 60:
                signal_type = 'bullish_momentum'
                trend = 'bullish'
                strength = 3
            elif rsi >= 40:
                signal_type = 'neutral'
                trend = 'neutral'
                strength = 2
            elif rsi >= 30:
                signal_type = 'bearish_momentum'
                trend = 'bearish'
                strength = 3
            elif rsi >= 20:
                signal_type = 'oversold'
                trend = 'bullish'
                strength = 4
            else:
                signal_type = 'extremely_oversold'
                trend = 'bullish'
                strength = 5

            return {
                'indicator': 'rsi',
                'signal_type': signal_type,
                'trend': trend,
                'strength': strength,
                'confidence': min(abs(rsi - 50) * 2, 100),
                'details': {
                    'rsi_value': rsi,
                    'distance_from_neutral': abs(rsi - 50)
                }
            }

        except Exception as e:
            logger.error(f"خطأ في تحليل RSI: {str(e)}")
            return None

    def _analyze_stochastic_signals(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """تحليل إشارات Stochastic"""
        try:
            stoch_k = data.get('stoch_k')
            stoch_d = data.get('stoch_d')

            if not all([stoch_k, stoch_d]):
                return None

            signals = []
            strength = 0

            # تحليل مستويات ذروة الشراء والبيع
            if stoch_k >= 80 and stoch_d >= 80:
                signals.append('overbought')
                trend = 'bearish'
                strength += 3
            elif stoch_k <= 20 and stoch_d <= 20:
                signals.append('oversold')
                trend = 'bullish'
                strength += 3
            else:
                trend = 'neutral'
                strength += 1

            # تحليل تقاطع %K و %D
            if stoch_k > stoch_d:
                signals.append('k_above_d')
                if trend == 'neutral':
                    trend = 'bullish'
                strength += 1
            else:
                signals.append('k_below_d')
                if trend == 'neutral':
                    trend = 'bearish'
                strength += 1

            return {
                'indicator': 'stochastic',
                'trend': trend,
                'strength': min(strength, 5),
                'confidence': min(strength * 20, 100),
                'details': {
                    'signals': signals,
                    'k_value': stoch_k,
                    'd_value': stoch_d
                }
            }

        except Exception as e:
            logger.error(f"خطأ في تحليل Stochastic: {str(e)}")
            return None

    def _analyze_williams_signals(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """تحليل إشارات Williams %R"""
        try:
            williams_r = data.get('williams_r')
            if not williams_r:
                return None

            # تحديد حالة Williams %R
            if williams_r >= -20:
                signal_type = 'overbought'
                trend = 'bearish'
                strength = 4
            elif williams_r >= -50:
                signal_type = 'neutral_to_bullish'
                trend = 'bullish'
                strength = 2
            elif williams_r >= -80:
                signal_type = 'neutral_to_bearish'
                trend = 'bearish'
                strength = 2
            else:
                signal_type = 'oversold'
                trend = 'bullish'
                strength = 4

            return {
                'indicator': 'williams_r',
                'signal_type': signal_type,
                'trend': trend,
                'strength': strength,
                'confidence': min(abs(williams_r + 50) * 2, 100),
                'details': {
                    'williams_value': williams_r
                }
            }

        except Exception as e:
            logger.error(f"خطأ في تحليل Williams %R: {str(e)}")
            return None

    def _analyze_cci_signals(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """تحليل إشارات CCI"""
        try:
            cci = data.get('cci')
            if not cci:
                return None

            # تحديد حالة CCI
            if cci >= 200:
                signal_type = 'extremely_overbought'
                trend = 'bearish'
                strength = 5
            elif cci >= 100:
                signal_type = 'overbought'
                trend = 'bearish'
                strength = 4
            elif cci >= 0:
                signal_type = 'bullish_momentum'
                trend = 'bullish'
                strength = 3
            elif cci >= -100:
                signal_type = 'bearish_momentum'
                trend = 'bearish'
                strength = 3
            elif cci >= -200:
                signal_type = 'oversold'
                trend = 'bullish'
                strength = 4
            else:
                signal_type = 'extremely_oversold'
                trend = 'bullish'
                strength = 5

            return {
                'indicator': 'cci',
                'signal_type': signal_type,
                'trend': trend,
                'strength': strength,
                'confidence': min(abs(cci) / 2, 100),
                'details': {
                    'cci_value': cci
                }
            }

        except Exception as e:
            logger.error(f"خطأ في تحليل CCI: {str(e)}")
            return None

    def _analyze_bollinger_signals(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """تحليل إشارات Bollinger Bands"""
        try:
            bb_upper = data.get('bb_upper')
            bb_middle = data.get('bb_middle')
            bb_lower = data.get('bb_lower')
            price = data.get('price', 0)

            if not all([bb_upper, bb_middle, bb_lower, price]):
                return None

            # حساب موقع السعر بالنسبة للنطاقات
            bb_range = bb_upper - bb_lower
            if bb_range == 0:
                return None

            # تحديد موقع السعر
            if price >= bb_upper:
                signal_type = 'touching_upper_band'
                trend = 'bearish'
                strength = 4
            elif price >= bb_middle + (bb_range * 0.3):
                signal_type = 'upper_zone'
                trend = 'bullish'
                strength = 3
            elif price >= bb_middle - (bb_range * 0.3):
                signal_type = 'middle_zone'
                trend = 'neutral'
                strength = 2
            elif price >= bb_lower:
                signal_type = 'lower_zone'
                trend = 'bearish'
                strength = 3
            else:
                signal_type = 'touching_lower_band'
                trend = 'bullish'
                strength = 4

            # حساب نسبة %B
            percent_b = (price - bb_lower) / bb_range

            return {
                'indicator': 'bollinger_bands',
                'signal_type': signal_type,
                'trend': trend,
                'strength': strength,
                'confidence': min(abs(percent_b - 0.5) * 200, 100),
                'details': {
                    'percent_b': percent_b,
                    'bb_width': bb_range,
                    'price_position': signal_type
                }
            }

        except Exception as e:
            logger.error(f"خطأ في تحليل Bollinger Bands: {str(e)}")
            return None

    # دوال دمج الإشارات والتحليلات
    def _combine_trend_signals(self, signals: List[Dict[str, Any]], timeframe_type: str) -> Dict[str, Any]:
        """دمج إشارات الاتجاه"""
        try:
            if not signals:
                return {'trend': 'unknown', 'strength': 0, 'confidence': 0}

            # حساب الأوزان حسب نوع الإطار الزمني
            weights = {
                'long_term': {'ichimoku': 0.4, 'moving_averages': 0.4, 'price_structure': 0.2},
                'medium_term': {'macd': 0.4, 'adx': 0.4, 'parabolic_sar': 0.2}
            }

            timeframe_weights = weights.get(timeframe_type, {})

            bullish_score = 0
            bearish_score = 0
            total_weight = 0

            for signal in signals:
                indicator = signal.get('indicator', '')
                trend = signal.get('trend', 'neutral')
                strength = signal.get('strength', 0)
                weight = timeframe_weights.get(indicator, 1.0)

                weighted_strength = strength * weight
                total_weight += weight

                if trend == 'bullish':
                    bullish_score += weighted_strength
                elif trend == 'bearish':
                    bearish_score += weighted_strength

            # تحديد الاتجاه النهائي
            if bullish_score > bearish_score:
                final_trend = 'bullish'
                final_strength = min(int(bullish_score / total_weight), 5) if total_weight > 0 else 0
            elif bearish_score > bullish_score:
                final_trend = 'bearish'
                final_strength = min(int(bearish_score / total_weight), 5) if total_weight > 0 else 0
            else:
                final_trend = 'neutral'
                final_strength = 2

            # حساب مستوى الثقة
            confidence = min(abs(bullish_score - bearish_score) * 10, 100) if total_weight > 0 else 0

            return {
                'trend': final_trend,
                'strength': final_strength,
                'confidence': confidence,
                'details': {
                    'bullish_score': bullish_score,
                    'bearish_score': bearish_score,
                    'signals_count': len(signals),
                    'individual_signals': signals
                }
            }

        except Exception as e:
            logger.error(f"خطأ في دمج إشارات الاتجاه: {str(e)}")
            return {'trend': 'unknown', 'strength': 0, 'confidence': 0}

    def _combine_entry_signals(self, signals: List[Dict[str, Any]]) -> Dict[str, Any]:
        """دمج إشارات الدخول"""
        try:
            if not signals:
                return {'signals': [], 'entry_quality': 0, 'timing_score': 0}

            # أوزان المؤشرات قصيرة المدى
            weights = {
                'rsi': 0.25,
                'stochastic': 0.25,
                'williams_r': 0.15,
                'cci': 0.15,
                'bollinger_bands': 0.20
            }

            bullish_signals = []
            bearish_signals = []
            total_strength = 0

            for signal in signals:
                indicator = signal.get('indicator', '')
                trend = signal.get('trend', 'neutral')
                strength = signal.get('strength', 0)
                weight = weights.get(indicator, 0.1)

                weighted_strength = strength * weight
                total_strength += weighted_strength

                if trend == 'bullish':
                    bullish_signals.append(signal)
                elif trend == 'bearish':
                    bearish_signals.append(signal)

            # تقييم جودة نقاط الدخول
            entry_quality = min(total_strength * 20, 100)

            # حساب نقاط التوقيت
            timing_score = self._calculate_timing_score(signals)

            return {
                'signals': signals,
                'entry_quality': entry_quality,
                'timing_score': timing_score,
                'bullish_signals': bullish_signals,
                'bearish_signals': bearish_signals,
                'total_signals': len(signals)
            }

        except Exception as e:
            logger.error(f"خطأ في دمج إشارات الدخول: {str(e)}")
            return {'signals': [], 'entry_quality': 0, 'timing_score': 0}

    def _calculate_timing_score(self, signals: List[Dict[str, Any]]) -> float:
        """حساب نقاط التوقيت للدخول"""
        try:
            if not signals:
                return 0

            # عوامل التوقيت
            timing_factors = {
                'oversold_signals': 0,
                'overbought_signals': 0,
                'momentum_signals': 0,
                'reversal_signals': 0
            }

            for signal in signals:
                signal_type = signal.get('signal_type', '')

                if 'oversold' in signal_type or 'extremely_oversold' in signal_type:
                    timing_factors['oversold_signals'] += 1
                elif 'overbought' in signal_type or 'extremely_overbought' in signal_type:
                    timing_factors['overbought_signals'] += 1
                elif 'momentum' in signal_type:
                    timing_factors['momentum_signals'] += 1
                elif 'touching' in signal_type:
                    timing_factors['reversal_signals'] += 1

            # حساب نقاط التوقيت
            timing_score = 0

            # إشارات ذروة البيع تعطي نقاط إيجابية للشراء
            timing_score += timing_factors['oversold_signals'] * 25

            # إشارات ذروة الشراء تعطي نقاط إيجابية للبيع
            timing_score += timing_factors['overbought_signals'] * 25

            # إشارات الزخم تعطي نقاط متوسطة
            timing_score += timing_factors['momentum_signals'] * 15

            # إشارات الانعكاس تعطي نقاط عالية
            timing_score += timing_factors['reversal_signals'] * 30

            return min(timing_score, 100)

        except Exception as e:
            logger.error(f"خطأ في حساب نقاط التوقيت: {str(e)}")
            return 0

    def _calculate_trend_alignment(self, long_term: Dict[str, Any], medium_term: Dict[str, Any]) -> Dict[str, Any]:
        """حساب التوافق بين الاتجاهات"""
        try:
            long_trend = long_term.get('trend', 'unknown')
            medium_trend = medium_term.get('trend', 'unknown')

            long_strength = long_term.get('strength', 0)
            medium_strength = medium_term.get('strength', 0)

            # تحديد مستوى التوافق
            if long_trend == medium_trend and long_trend != 'neutral':
                alignment_type = 'strong_alignment'
                alignment_score = min((long_strength + medium_strength) * 10, 100)
            elif long_trend == 'neutral' or medium_trend == 'neutral':
                alignment_type = 'partial_alignment'
                alignment_score = max(long_strength, medium_strength) * 10
            else:
                alignment_type = 'conflicting'
                alignment_score = abs(long_strength - medium_strength) * 5

            return {
                'alignment_type': alignment_type,
                'alignment_score': alignment_score,
                'long_term_trend': long_trend,
                'medium_term_trend': medium_trend,
                'trend_consistency': long_trend == medium_trend
            }

        except Exception as e:
            logger.error(f"خطأ في حساب التوافق بين الاتجاهات: {str(e)}")
            return {'alignment_type': 'unknown', 'alignment_score': 0}

    def _evaluate_entry_quality(self, short_term: Dict[str, Any], trend_alignment: Dict[str, Any]) -> float:
        """تقييم جودة نقاط الدخول"""
        try:
            entry_quality = short_term.get('entry_quality', 0)
            timing_score = short_term.get('timing_score', 0)
            alignment_score = trend_alignment.get('alignment_score', 0)

            # حساب الجودة الإجمالية
            base_quality = (entry_quality * 0.4) + (timing_score * 0.3) + (alignment_score * 0.3)

            # تعديل الجودة بناءً على نوع التوافق
            alignment_type = trend_alignment.get('alignment_type', 'unknown')
            if alignment_type == 'strong_alignment':
                base_quality *= 1.2
            elif alignment_type == 'conflicting':
                base_quality *= 0.7

            return min(base_quality, 100)

        except Exception as e:
            logger.error(f"خطأ في تقييم جودة نقاط الدخول: {str(e)}")
            return 0

    def _calculate_overall_confidence(self, long_term: Dict[str, Any], medium_term: Dict[str, Any],
                                    short_term: Dict[str, Any]) -> float:
        """حساب مستوى الثقة الإجمالي"""
        try:
            long_confidence = long_term.get('confidence', 0)
            medium_confidence = medium_term.get('confidence', 0)
            short_confidence = short_term.get('timing_score', 0)

            # حساب الثقة الإجمالية بأوزان مختلفة
            overall_confidence = (
                long_confidence * 0.4 +
                medium_confidence * 0.4 +
                short_confidence * 0.2
            )

            return min(overall_confidence, 100)

        except Exception as e:
            logger.error(f"خطأ في حساب مستوى الثقة الإجمالي: {str(e)}")
            return 0

    def _generate_recommendation(self, long_term: Dict[str, Any], medium_term: Dict[str, Any],
                               short_term: Dict[str, Any], trend_alignment: Dict[str, Any],
                               entry_quality: float) -> Dict[str, Any]:
        """إنشاء التوصية النهائية"""
        try:
            # تحديد الاتجاه الرئيسي
            long_trend = long_term.get('trend', 'unknown')
            medium_trend = medium_term.get('trend', 'unknown')
            alignment_type = trend_alignment.get('alignment_type', 'unknown')

            # تحديد نوع التوصية
            if alignment_type == 'strong_alignment' and entry_quality > 70:
                if long_trend == 'bullish':
                    recommendation_type = 'strong_buy'
                    action = 'buy'
                else:
                    recommendation_type = 'strong_sell'
                    action = 'sell'
                confidence_level = 'high'
            elif alignment_type == 'strong_alignment' and entry_quality > 50:
                if long_trend == 'bullish':
                    recommendation_type = 'buy'
                    action = 'buy'
                else:
                    recommendation_type = 'sell'
                    action = 'sell'
                confidence_level = 'medium'
            elif entry_quality > 60:
                recommendation_type = 'weak_signal'
                action = long_trend if long_trend != 'neutral' else 'hold'
                confidence_level = 'low'
            else:
                recommendation_type = 'hold'
                action = 'hold'
                confidence_level = 'very_low'

            return {
                'recommendation_type': recommendation_type,
                'action': action,
                'confidence_level': confidence_level,
                'entry_quality': entry_quality,
                'primary_trend': long_trend,
                'secondary_trend': medium_trend,
                'alignment_quality': alignment_type
            }

        except Exception as e:
            logger.error(f"خطأ في إنشاء التوصية النهائية: {str(e)}")
            return {
                'recommendation_type': 'hold',
                'action': 'hold',
                'confidence_level': 'very_low',
                'entry_quality': 0
            }
